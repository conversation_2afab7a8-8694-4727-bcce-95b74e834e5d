import * as React from 'react';
import { <PERSON><PERSON>, Loader } from '@fluentui/react-northstar';

// CSS
import './TeamsSaveButton.scss';

export interface ITeamsSaveButtonProps {
  isSaving: boolean;
  isSaveCompleted: boolean;
  isDisabled: boolean;
  onClick: () => Promise<void>;
  className?: string;
}

/**
 * TeamsSaveButton
 * 保存ボタンを担当するコンポーネント
 */
const TeamsSaveButton: React.FC<ITeamsSaveButtonProps> = (props) => {
  const {
    isSaving,
    isSaveCompleted,
    isDisabled,
    onClick,
    className = '',
  } = props;

  // 保存ボタンのコンテンツ
  const saveButtonContent = React.useMemo(() => {
    if (isSaving) {
      return (
        <>
          <Loader size="smallest" inline />
          {' '}
          保存中...
        </>
      );
    }
    if (isSaveCompleted) {
      return '保存完了';
    }
    return '保存';
  }, [isSaving, isSaveCompleted]);

  // 保存ボタンのスタイル
  const saveButtonStyles = React.useMemo(() => {
    if (isSaveCompleted) {
      return {
        root: {
          backgroundColor: '#107c10',
          borderColor: '#107c10',
          color: 'white',
        },
      };
    }
    return undefined;
  }, [isSaveCompleted]);

  // クリックハンドラー
  const handleClick = React.useCallback(async () => {
    if (isDisabled || isSaving || isSaveCompleted) {
      return;
    }

    try {
      await onClick();
    } catch (error) {
      // エラーハンドリングは親コンポーネントに委譲
      console.error('保存処理でエラーが発生しました:', error);
    }
  }, [onClick, isDisabled, isSaving, isSaveCompleted]);

  return (
    <div className={`teams-save-button ${className}`}>
      <Button
        primary={!isSaveCompleted}
        content={saveButtonContent}
        disabled={isDisabled}
        onClick={handleClick}
        fluid
        styles={saveButtonStyles}
        className={`teams-save-button-element ${isSaveCompleted ? 'completed' : ''} ${isSaving ? 'saving' : ''}`}
      />
    </div>
  );
};

export default TeamsSaveButton;
