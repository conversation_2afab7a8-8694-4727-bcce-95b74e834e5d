import * as React from 'react';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';
import { TeamsSettingTabType, TeamsSettingTabTypeValue } from '../../components/domains/app-info/teamsSettingModal/TeamsSettingTabs';
import { ToasterMessage } from '../../components/commons/molecules/message-toaster/MessageToaster';
import { ExtendPopupTimer } from '../behaviors/useMessageToasterBehavior';

export interface UseTeamsSettingSelectionProps {
  allChatItems: IUserChatItem[];
  savedItems: Set<string>;
  extendPopupTimer: ExtendPopupTimer;
  maxSelectionCount?: number;
}

export interface UseTeamsSettingSelectionReturnType {
  // 選択状態
  selectedItems: Set<string>;
  activeTab: TeamsSettingTabTypeValue;
  searchQuery: string;
  filteredChatItems: IUserChatItem[];

  // 計算値
  chatCount: number;
  channelCount: number;
  searchPlaceholder: string;

  // イベントハンドラー
  handleItemToggle: (id: string) => void;
  handleRemoveSelectedItem: (id: string) => void;
  handleKeyDown: (event: React.KeyboardEvent, id: string) => void;
  handleTabChange: (tab: TeamsSettingTabTypeValue) => void;
  handleSearchQueryChange: (
    e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => void;

  // 状態更新関数
  setSelectedItems: React.Dispatch<React.SetStateAction<Set<string>>>;
  resetSelection: () => void;
}

/**
 * Teams設定の選択状態管理ロジックを管理するカスタムフック
 */
const useTeamsSettingSelection = (
  props: UseTeamsSettingSelectionProps,
): UseTeamsSettingSelectionReturnType => {
  const {
    allChatItems,
    savedItems,
    extendPopupTimer,
    maxSelectionCount = 10,
  } = props;

  // 選択状態管理
  const [selectedItems, setSelectedItems] = React.useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = React.useState<TeamsSettingTabTypeValue>(
    TeamsSettingTabType.CHAT,
  );
  const [searchQuery, setSearchQuery] = React.useState('');
  const [filteredChatItems, setFilteredChatItems] = React.useState<IUserChatItem[]>([]);

  // 保存済みアイテムが変更された時に選択状態を同期
  React.useEffect(() => {
    setSelectedItems(new Set(savedItems));
  }, [savedItems]);

  // 検索フィルタリングのEffect（タブとテキスト検索の両方に対応）
  React.useEffect(() => {
    let filtered = allChatItems;

    // タブによるフィルタリング
    filtered = filtered.filter((item) => item.type === activeTab);

    // テキスト検索によるフィルタリング
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (item) => item.id.toLowerCase().includes(searchQuery.toLowerCase())
        || item.name.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    setFilteredChatItems(filtered);
  }, [searchQuery, allChatItems, activeTab]);

  // チャットとチャネルの件数を計算
  const chatCount = React.useMemo(
    () => allChatItems.filter((item) => item.type === TeamsSettingTabType.CHAT).length,
    [allChatItems],
  );

  const channelCount = React.useMemo(
    () => allChatItems.filter((item) => item.type === TeamsSettingTabType.CHANNEL).length,
    [allChatItems],
  );

  // プレースホルダーテキストをタブに応じて変更
  const searchPlaceholder = React.useMemo(() => (activeTab === TeamsSettingTabType.CHAT ? 'チャット名で検索' : 'チャネル名で検索'),
    [activeTab]);

  // アイテム選択切り替えハンドラー
  const handleItemToggle = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        // 選択解除の場合
        newSet.delete(id);
      } else {
        // 選択追加の場合：上限チェック
        if (newSet.size >= maxSelectionCount) {
          // 上限に達している場合はトースターメッセージを表示
          extendPopupTimer(ToasterMessage.MAX_TEAMS_SELECTION);
          return prev; // 状態を変更しない
        }
        newSet.add(id);
      }
      return newSet;
    });
  }, [extendPopupTimer, maxSelectionCount]);

  // 選択されたアイテムを削除するハンドラー
  const handleRemoveSelectedItem = React.useCallback((id: string) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  }, []);

  // キーボードイベントハンドラー
  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, id: string) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemToggle(id);
    }
  }, [handleItemToggle]);

  // タブ変更ハンドラー
  const handleTabChange = React.useCallback((tab: TeamsSettingTabTypeValue) => {
    setActiveTab(tab);
    // タブ切り替え時に検索クエリをクリア
    setSearchQuery('');
  }, []);

  // 検索クエリ入力の変更ハンドラー
  const handleSearchQueryChange = React.useCallback(
    (_e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => {
      setSearchQuery(data?.value ?? '');
    },
    [],
  );

  // 選択状態をリセットする関数
  const resetSelection = React.useCallback(() => {
    setSelectedItems(new Set());
    setActiveTab(TeamsSettingTabType.CHAT);
    setSearchQuery('');
  }, []);

  return {
    // 選択状態
    selectedItems,
    activeTab,
    searchQuery,
    filteredChatItems,

    // 計算値
    chatCount,
    channelCount,
    searchPlaceholder,

    // イベントハンドラー
    handleItemToggle,
    handleRemoveSelectedItem,
    handleKeyDown,
    handleTabChange,
    handleSearchQueryChange,

    // 状態更新関数
    setSelectedItems,
    resetSelection,
  };
};

export default useTeamsSettingSelection;
