import * as React from 'react';
import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import useTeamsChatsApiAccessor, {
  DeleteTeamsChatsApi,
  PostTeamsChatsApi,
  ITeamsChatsRequest,
} from '../accessors/useTeamsChatsApiAccessor';
import useTeamsChatsRepositoryAccessor, {
  AddTeamsChatsQueue,
  DeleteTeamsChatsQueue,
  GetTeamsChatsQueues,
  ReplaceTeamsChatsQueues,
} from '../accessors/useTeamsChatsRepositoryAccessor';
import { IRepositoryTeamsChatsQueue, ITeamsChatsItem } from '../../types/IGeraniumAttaneDB';

export type AddRemoteTeamsChats = (data: ITeamsChatsItem) => Promise<void>;
export type DeleteRemoteTeamsChats = (data: ITeamsChatsItem) => Promise<void>;
export type FetchRemoteTeamsChats = () => Promise<void>;

type UseRemoteTeamsChatsFeatureReturnType = {
  addRemoteTeamsChats: AddRemoteTeamsChats | undefined,
  deleteRemoteTeamsChats: DeleteRemoteTeamsChats | undefined,
  fetchRemoteTeamsChats: FetchRemoteTeamsChats | undefined,
};

export const UseRemoteTeamsChatsError = {
  IS_FIRST_TIME_SYNC: 'IS_FIRST_TIME_SYNC',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
  QUEUE_SEND_FAIL: 'QUEUE_SEND_FAIL',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
};

/**
 * TeamsChatsアイテムをAPIリクエスト形式に変換
 */
function convertToApiRequest(item: ITeamsChatsItem): ITeamsChatsRequest {
  return {
    countId: item.countId,
    chatType: item.chatType,
    ...(item.type === 'チャット' && {
      chatId: item.id,
    }),
    ...(item.type === 'チャネル' && {
      teamId: item.teamId,
      channelId: item.id,
    }),
  };
}

/**
 * 1件づつQueueを送信し、成功したらqueueから消す
 * 失敗したらそれ以上は送信しない
 */
export function sendQueue(
  q: IRepositoryTeamsChatsQueue,
  postTeamsChatsApi: PostTeamsChatsApi,
  deleteTeamsChatsApi: DeleteTeamsChatsApi,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue,
): Promise<void> {

  // PUTの場合
  if (q.type === 'PUT') {
    const request = convertToApiRequest(q.data);
    return postTeamsChatsApi(request)
      .then(() => deleteTeamsChatsQueue(q.data.id));
  }

  // DELETEの場合
  return deleteTeamsChatsApi(q.data.id)
    .then(() => deleteTeamsChatsQueue(q.data.id));
}

/**
 * キューを直列で逐次送信し、成功したキューは削除する
 * 途中で失敗した場合はキューを保持し、送信を止める
 */
export async function sendQueues(
  queues: IRepositoryTeamsChatsQueue[] | null,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue | undefined,
  eventReporter: EventReporter,
): Promise<void> {

  if (!queues || !postTeamsChatsApi || !deleteTeamsChatsApi || !deleteTeamsChatsQueue) {
    return Promise.resolve();
  }

  // 0件時はwhileを回さずに終了
  if (queues.length === 0) {
    return Promise.resolve();
  }

  // whileループを開始
  let isContinue = true;
  let counter = 0;

  while (isContinue) {
    const q = queues[counter];
    if (!q) {
      isContinue = false;
      break;
    }

    // 直列処理したいのでPromise.allにしない
    // キューの中身を逐次APIに投げ、失敗したらそこで止める
    // eslint-disable-next-line no-await-in-loop
    const result = await sendQueue(q, postTeamsChatsApi, deleteTeamsChatsApi, deleteTeamsChatsQueue)
      // Error型でcatchできるはずだが、whileを抜けられるように必ずError型を返させる
      .catch((reason: Error | unknown) => (reason instanceof Error
        ? reason
        : new Error(UseRemoteTeamsChatsError.UNKNOWN_ERROR)));

    // 失敗した場合はwhileを抜ける
    if (result instanceof Error) {
      eventReporter({
        type: EventReportType.SYS_ERROR,
        name: UseRemoteTeamsChatsError.QUEUE_SEND_FAIL,
        error: result,
      });
      isContinue = false;
      break;
    }

    counter += 1;
  }

  return Promise.resolve();
}

/**
 * ローカルとリモートの両方にTeamsChatsを追加する機能の実装
 */
export async function addRemoteTeamsChatsImpl(
  addTeamsChatsQueue: AddTeamsChatsQueue | undefined,
  getTeamsChatsQueues: GetTeamsChatsQueues | undefined,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  item: ITeamsChatsItem,
  eventReporter: EventReporter,
): Promise<void> {

  if (
    !getTeamsChatsQueues
    || !addTeamsChatsQueue) {
    return Promise.resolve();
  }

  // キューとTeamsChatsの両方に追加
  await addTeamsChatsQueue(item, 'PUT');

  // キュー送信を試行
  return sendQueues(
    await getTeamsChatsQueues(),
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  );
}

/**
 * ローカルとリモートの両方からTeamsChatsを削除する機能の実装
 */
export async function deleteRemoteTeamsChatsImpl(
  addTeamsChatsQueue: AddTeamsChatsQueue | undefined,
  getTeamsChatsQueues: GetTeamsChatsQueues | undefined,
  deleteTeamsChatsQueue: DeleteTeamsChatsQueue | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  item: ITeamsChatsItem,
  eventReporter: EventReporter,
): Promise<void> {

  if (
    !getTeamsChatsQueues
    || !addTeamsChatsQueue
  ) {
    return Promise.resolve();
  }

  // 削除キューの追加とTeamsChats削除
  await addTeamsChatsQueue(item, 'DELETE');

  // キュー送信を試行
  return sendQueues(
    await getTeamsChatsQueues(),
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  );
}

/**
 * queueをTeamsChatsのPUTで全件置き換える
 */
export function replaceQueuesByTeamsChats(
  teamsChats: ITeamsChatsItem[],
  replaceTeamsChatsQueues: ReplaceTeamsChatsQueues,
): Promise<void> {

  const queues: IRepositoryTeamsChatsQueue[] = teamsChats.map((item) => ({
    type: 'PUT',
    data: item,
    date: new Date(),
  }));

  return replaceTeamsChatsQueues(queues);
}

/**
 * リモート上のAPIとブラウザローカルのTeamsChatsデータの追加/削除/同期処理をする機能
 */
const useRemoteTeamsChatsFeature = (
  useRepositoryReturn: ReturnType<typeof useTeamsChatsRepositoryAccessor>,
  useTeamsChatsApiReturn: ReturnType<typeof useTeamsChatsApiAccessor>,
  eventReporter: EventReporter,
): UseRemoteTeamsChatsFeatureReturnType => {

  // useRepositoryからメンバ取得
  const {
    replaceTeamsChats,
    getTeamsChatsQueues,
    addTeamsChatsQueue,
    deleteTeamsChatsQueue,
    replaceTeamsChatsQueues,
  } = useRepositoryReturn;

  // useApiからメンバ取得
  const {
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
  } = useTeamsChatsApiReturn;

  const [isInitialized, setIsInitialized] = React.useState<'INITIAL' | 'PENDING' | 'DONE'>('INITIAL');
  const initialRunTrigger = React.useRef(false);

  /**
   * リモート取得処理の実装
   */
  const fetchRemoteTeamsChats = React.useCallback(async (onSuccess?: () => void) => {
    if (isInitialized !== 'PENDING') {
      // 簡単な同期処理（お気に入りほど複雑ではない）
      setIsInitialized('PENDING');

      try {
        // キュー送信を試行
        await sendQueues(
          await getTeamsChatsQueues(),
          postTeamsChatsApi,
          deleteTeamsChatsApi,
          deleteTeamsChatsQueue,
          eventReporter,
        );
      } finally {
        setIsInitialized('DONE');
      }

      if (onSuccess) {
        onSuccess();
      }
    }
  }, [
    isInitialized,
    getTeamsChatsQueues,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  ]);

  /**
   * ローカルとリモートの両方にTeamsChats追加
   */
  const addRemoteTeamsChats: AddRemoteTeamsChats = React.useCallback(
    async (item: ITeamsChatsItem) => addRemoteTeamsChatsImpl(
      addTeamsChatsQueue,
      getTeamsChatsQueues,
      deleteTeamsChatsQueue,
      deleteTeamsChatsApi,
      postTeamsChatsApi,
      item,
      eventReporter,
    ),
    [
      addTeamsChatsQueue,
      deleteTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      eventReporter,
    ],
  );

  /**
   * ローカルとリモートの両方からTeamsChats削除
   */
  const deleteRemoteTeamsChats: DeleteRemoteTeamsChats = React.useCallback(
    async (item: ITeamsChatsItem) => deleteRemoteTeamsChatsImpl(
      addTeamsChatsQueue,
      getTeamsChatsQueues,
      deleteTeamsChatsQueue,
      deleteTeamsChatsApi,
      postTeamsChatsApi,
      item,
      eventReporter,
    ),
    [
      addTeamsChatsQueue,
      deleteTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      eventReporter,
    ],
  );

  // 初回同期処理の実行
  React.useEffect(() => {
    if (
      !getTeamsChatsApi
      || !getTeamsChatsQueues
      || !replaceTeamsChats
      || !addTeamsChatsQueue
      || !replaceTeamsChatsQueues
    ) return;

    if (initialRunTrigger.current) return;

    initialRunTrigger.current = true;
    fetchRemoteTeamsChats();
  }, [
    fetchRemoteTeamsChats,
    getTeamsChatsApi,
    addTeamsChatsQueue,
    getTeamsChatsQueues,
    replaceTeamsChatsQueues,
    replaceTeamsChats,
  ]);

  return {
    addRemoteTeamsChats,
    deleteRemoteTeamsChats,
    fetchRemoteTeamsChats,
  };
};

export default useRemoteTeamsChatsFeature;
