import * as React from 'react';
import { Input } from '@fluentui/react-northstar';

// CSS
import './TeamsSearchInput.scss';

export interface ITeamsSearchInputProps {
  placeholder: string;
  value: string;
  onChange: (e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => void;
  disabled?: boolean;
  className?: string;
}

/**
 * TeamsSearchInput
 * Teams設定モーダルの検索入力フィールドコンポーネント
 */
const TeamsSearchInput: React.FC<ITeamsSearchInputProps> = (props) => {
  const {
    placeholder,
    value,
    onChange,
    disabled = false,
    className = '',
  } = props;

  return (
    <div className={`teams-search-input ${className}`}>
      <Input
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        fluid
        className="teams-search-input-field"
      />
    </div>
  );
};

export default TeamsSearchInput;
