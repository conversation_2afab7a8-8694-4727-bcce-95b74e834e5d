import * as React from 'react';
import { Header } from '@fluentui/react-northstar';
import TeamsSettingTabs, { TeamsSettingTabTypeValue } from './TeamsSettingTabs';
import SelectedItemsList from './SelectedItemsList';
import TeamsSearchInput from './TeamsSearchInput';
import TeamsItemsList from './TeamsItemsList';
import TeamsSaveButton from './TeamsSaveButton';
import { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';

// CSS
import './TeamsSettingContent.scss';

export interface ITeamsSettingContentProps {
  // データ関連
  allChatItems: IUserChatItem[];
  filteredChatItems: IUserChatItem[];
  selectedItems: Set<string>;
  isLoadingData: boolean;
  error: string | null;
  // タブ関連
  activeTab: TeamsSettingTabTypeValue;
  chatCount: number;
  channelCount: number;
  // 検索関連
  searchQuery: string;
  searchPlaceholder: string;
  // 保存関連
  isSaving: boolean;
  isSaveCompleted: boolean;
  isSaveDisabled: boolean;
  // イベントハンドラー
  onTabChange: (tab: TeamsSettingTabTypeValue) => void;
  onSearchQueryChange: (e: React.SyntheticEvent<HTMLElement>, data?: { value?: string }) => void;
  onItemToggle: (id: string) => void;
  onRemoveSelectedItem: (id: string) => void;
  onKeyDown: (event: React.KeyboardEvent, id: string) => void;
  onSave: () => Promise<void>;
  // オプション
  className?: string;
}

/**
 * TeamsSettingContent
 * モーダルの中身を担当するコンポーネント
 */
const TeamsSettingContent: React.FC<ITeamsSettingContentProps> = (props) => {
  const {
    // データ関連
    allChatItems,
    filteredChatItems,
    selectedItems,
    isLoadingData,
    error,
    // タブ関連
    activeTab,
    chatCount,
    channelCount,
    // 検索関連
    searchQuery,
    searchPlaceholder,
    // 保存関連
    isSaving,
    isSaveCompleted,
    isSaveDisabled,
    // イベントハンドラー
    onTabChange,
    onSearchQueryChange,
    onItemToggle,
    onRemoveSelectedItem,
    onKeyDown,
    onSave,
    // オプション
    className = '',
  } = props;

  /**
   * ラベル
   */
  const TeamsSettingLabel = {
    TITLE: 'Teams設定',
    DESCRIPTION: '検索対象を選択できます。',
  };

  return (
    <div className={`teams-setting-content ${className}`}>
      {/* ヘッダー */}
      <div className="teams-setting-content-header">
        <Header
          content={TeamsSettingLabel.TITLE}
          as="h3"
          className="teams-setting-content-title"
        />
      </div>

      {/* メインコンテンツ */}
      <div className="teams-setting-content-main">
        <p className="teams-setting-content-description">
          {TeamsSettingLabel.DESCRIPTION}
        </p>

        {/* タブ切り替え */}
        <div className="teams-setting-content-tabs">
          <TeamsSettingTabs
            activeTab={activeTab}
            onTabChange={onTabChange}
            disabled={isLoadingData}
            chatCount={chatCount}
            channelCount={channelCount}
          />
        </div>

        {/* 検索フィールド */}
        <div className="teams-setting-content-search">
          <TeamsSearchInput
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={onSearchQueryChange}
            disabled={isLoadingData}
          />
        </div>

        {/* 選択されたアイテム一覧 */}
        <div className="teams-setting-content-selected">
          <SelectedItemsList
            selectedItems={selectedItems}
            allChatItems={allChatItems}
            onRemoveItem={onRemoveSelectedItem}
          />
        </div>

        {/* チャットアイテム一覧 */}
        <div className="teams-setting-content-items">
          <TeamsItemsList
            filteredChatItems={filteredChatItems}
            selectedItems={selectedItems}
            isLoading={isLoadingData}
            error={error}
            onItemToggle={onItemToggle}
            onKeyDown={onKeyDown}
          />
        </div>

        {/* 保存ボタン */}
        <div className="teams-setting-content-save">
          <TeamsSaveButton
            isSaving={isSaving}
            isSaveCompleted={isSaveCompleted}
            isDisabled={isSaveDisabled}
            onClick={onSave}
          />
        </div>
      </div>
    </div>
  );
};

TeamsSettingContent.defaultProps = {
  className: undefined,
};

export default TeamsSettingContent;
