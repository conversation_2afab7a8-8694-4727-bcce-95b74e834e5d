import '@testing-library/jest-dom';
import { renderHook, act } from '@testing-library/react-hooks';
import useTeamsSettingData, { UseTeamsSettingDataProps } from './useTeamsSettingData';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';

// environment をモック
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

// テスト用のモックデータ
const createMockUserChatItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
  id: 'test-chat-id',
  name: 'テストチャット',
  type: 'チャット',
  chatType: 'oneOnOne',
  ...overrides,
});

const createMockChannelItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
  id: 'test-channel-id',
  name: 'テストチャネル',
  type: 'チャネル',
  chatType: 'channel',
  teamId: 'test-team-id',
  ...overrides,
});

describe('useTeamsSettingData', () => {
  // モック関数
  const fetchUserChatsAndChannelsMock = jest.fn();
  const getTeamsChatsApiMock = jest.fn();
  const postTeamsChatsApiMock = jest.fn();
  const deleteTeamsChatsApiMock = jest.fn();

  // デフォルトのprops
  const defaultProps: UseTeamsSettingDataProps = {
    fetchUserChatsAndChannels: fetchUserChatsAndChannelsMock,
    getTeamsChatsApi: getTeamsChatsApiMock,
    postTeamsChatsApi: postTeamsChatsApiMock,
    deleteTeamsChatsApi: deleteTeamsChatsApiMock,
    isModalOpen: false,
  };

  beforeEach(() => {
    // 各テスト前にモックをクリア
    fetchUserChatsAndChannelsMock.mockClear();
    getTeamsChatsApiMock.mockClear();
    postTeamsChatsApiMock.mockClear();
    deleteTeamsChatsApiMock.mockClear();
  });

  describe('初期状態のテスト', () => {
    it('初期状態では空の配列とSetが設定されている', () => {
      // 初期状態をテスト
      const { result } = renderHook(() => useTeamsSettingData(defaultProps));

      expect(result.current.allChatItems).toEqual([]);
      expect(result.current.savedItems).toEqual(new Set());
      expect(result.current.isLoadingSavedItems).toBe(false);
    });
  });

  describe('データ取得のテスト', () => {
    it('モーダルが開いた時にデータを取得する', async () => {
      // モックデータを設定
      const mockChatItems = [createMockUserChatItem(), createMockChannelItem()];
      const mockSavedChats = [
        { chatId: 'test-chat-id', channelId: null },
        { chatId: null, channelId: 'test-channel-id' },
      ];

      fetchUserChatsAndChannelsMock.mockResolvedValue(mockChatItems);
      getTeamsChatsApiMock.mockResolvedValue(mockSavedChats);

      const { result } = renderHook(() => useTeamsSettingData({
        ...defaultProps,
        isModalOpen: true,
      }));

      // データ取得が完了するまで待機
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      expect(fetchUserChatsAndChannelsMock).toHaveBeenCalledTimes(1);
      expect(getTeamsChatsApiMock).toHaveBeenCalledTimes(1);
      expect(result.current.allChatItems).toEqual(mockChatItems);
      expect(result.current.savedItems).toEqual(new Set(['test-chat-id', 'test-channel-id']));
    });

    it('モーダルが閉じている時はデータを取得しない', () => {
      // モーダルが閉じている状態でテスト
      renderHook(() => useTeamsSettingData({
        ...defaultProps,
        isModalOpen: false,
      }));

      expect(fetchUserChatsAndChannelsMock).not.toHaveBeenCalled();
      expect(getTeamsChatsApiMock).not.toHaveBeenCalled();
    });
  });

  describe('保存済みアイテム取得のテスト', () => {
    it('loadSavedItems関数が正常に動作する', async () => {
      // モックデータを設定
      const mockSavedChats = [
        { chatId: 'chat-1', channelId: null },
        { chatId: null, channelId: 'channel-1' },
      ];
      getTeamsChatsApiMock.mockResolvedValue(mockSavedChats);

      const { result } = renderHook(() => useTeamsSettingData(defaultProps));

      await act(async () => {
        await result.current.loadSavedItems();
      });

      expect(getTeamsChatsApiMock).toHaveBeenCalledTimes(1);
      expect(result.current.savedItems).toEqual(new Set(['chat-1', 'channel-1']));
    });

    it('getTeamsChatsApiが未定義の場合は何もしない', async () => {
      // getTeamsChatsApiが未定義の場合をテスト
      const { result } = renderHook(() => useTeamsSettingData({
        ...defaultProps,
        getTeamsChatsApi: undefined,
      }));

      await act(async () => {
        await result.current.loadSavedItems();
      });

      expect(result.current.savedItems).toEqual(new Set());
    });

    it('API呼び出しでエラーが発生した場合はエラーをスローする', async () => {
      // API呼び出しでエラーが発生する場合をテスト
      const errorMessage = 'API Error';
      getTeamsChatsApiMock.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useTeamsSettingData(defaultProps));

      await act(async () => {
        await expect(result.current.loadSavedItems()).rejects.toThrow('保存済みアイテムの取得に失敗しました');
      });

      expect(result.current.isLoadingSavedItems).toBe(false);
    });
  });

  describe('アイテム保存のテスト', () => {
    it('saveSelectedItems関数が正常に動作する', async () => {
      // モックデータを設定
      const mockChatItems = [createMockUserChatItem({ id: 'chat-1' })];
      const selectedItems = new Set(['chat-1']);

      postTeamsChatsApiMock.mockResolvedValue(undefined);
      deleteTeamsChatsApiMock.mockResolvedValue(undefined);

      const { result } = renderHook(() => useTeamsSettingData(defaultProps));

      await act(async () => {
        await result.current.saveSelectedItems(selectedItems, mockChatItems);
      });

      expect(postTeamsChatsApiMock).toHaveBeenCalledTimes(1);
      expect(result.current.savedItems).toEqual(selectedItems);
    });

    it('API関数が未定義の場合はエラーをスローする', async () => {
      // API関数が未定義の場合をテスト
      const { result } = renderHook(() => useTeamsSettingData({
        ...defaultProps,
        postTeamsChatsApi: undefined,
        deleteTeamsChatsApi: undefined,
      }));

      const selectedItems = new Set(['chat-1']);
      const mockChatItems = [createMockUserChatItem()];

      await act(async () => {
        await expect(result.current.saveSelectedItems(selectedItems, mockChatItems))
          .rejects.toThrow('API関数が利用できません');
      });
    });

    it('削除処理が正常に動作する', async () => {
      // 保存済みアイテムがある状態で削除をテスト
      const mockSavedChats = [{ chatId: 'chat-1', channelId: null }];
      getTeamsChatsApiMock.mockResolvedValue(mockSavedChats);
      deleteTeamsChatsApiMock.mockResolvedValue(undefined);

      const { result } = renderHook(() => useTeamsSettingData(defaultProps));

      // まず保存済みアイテムを設定
      await act(async () => {
        await result.current.loadSavedItems();
      });

      // 空の選択で保存（削除のみ）
      await act(async () => {
        await result.current.saveSelectedItems(new Set(), []);
      });

      expect(deleteTeamsChatsApiMock).toHaveBeenCalledWith('chat-1');
      expect(result.current.savedItems).toEqual(new Set());
    });
  });
});
