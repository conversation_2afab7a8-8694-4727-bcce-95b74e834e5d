// import * as React from 'react';
// import { Button } from '@fluentui/react-northstar';
// import { CloseIcon } from '@fluentui/react-icons-northstar';
// import { mergedClassName } from '../../../../utilities/commonFunction';
// import ModalCardTop from '../../../commons/molecules/modal-card-top/ModalCardTop';
// import TeamsSettingContent from './TeamsSettingContent';
// import MessageToaster from '../../../commons/molecules/message-toaster/MessageToaster';
// import useTeamsSettingModal from '../../../../hooks/features/useTeamsSettingModal';
// import { UseTeamsChatsApiReturnType }
//  from '../../../../hooks/accessors/useTeamsChatsApiAccessor';

// // CSS
// import './TeamsSettingModal.scss';

// export interface ISimpleModalProps {
//   className?: string;
//   open?: boolean;
//   onClose: () => void;
//   useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType;
// }

// /**
//  * TeamsSettingModal
//  * @param props
//  */
// const TeamsSettingModal: React.FC<ISimpleModalProps> = (props) => {
//   const {
//     className,
//     open,
//     onClose,
//     useTeamsChatsApiAccessorReturn,
//   } = props;

//   // メインのフックを使用して状態とロジックを管理
//   const teamsSettingModal = useTeamsSettingModal({
//     open: open || false,
//     onClose,
//     useTeamsChatsApiAccessorReturn,
//   });

//   // マージされたCSSクラス名
//   const rootClassName = React.useMemo(() => {
//     const step1 = mergedClassName('simple-modal', className);
//     const isOpen = open ? 'is-open' : '';
//     const hasSelectedItems =
// teamsSettingModal.selectedItems.size > 0 ? 'has-selected-items' : '';
//     return mergedClassName(mergedClassName(isOpen, step1), hasSelectedItems);
//   }, [className, open, teamsSettingModal.selectedItems.size]);

//   return (
//     <div className={rootClassName}>
//       {/* SP用閉じるボタン */}
//       <div className="simple-modal-edge">
//         <ModalCardTop
//           showBookmark={false}
//           onClickClose={teamsSettingModal.handleClose}
//         />
//       </div>

//       {/* PC用閉じるボタン */}
//       <div className="simple-modal-close-pc">
//         <Button
//           className="simple-modal-close-pc-button"
//           icon={<CloseIcon />}
//           text
//           iconOnly
//           onClick={teamsSettingModal.handleClose}
//         />
//       </div>

//       <div className="simple-modal-scroll-wrapper">
//         <div className="simple-modal-scroll-inner">
//           {/* メインコンテンツ */}
//           <TeamsSettingContent
//             // データ関連
//             allChatItems={teamsSettingModal.allChatItems}
//             filteredChatItems={teamsSettingModal.filteredChatItems}
//             selectedItems={teamsSettingModal.selectedItems}
//             isLoadingData={teamsSettingModal.isLoadingData}
//             error={teamsSettingModal.error}
//             // タブ関連
//             activeTab={teamsSettingModal.activeTab}
//             chatCount={teamsSettingModal.chatCount}
//             channelCount={teamsSettingModal.channelCount}
//             // 検索関連
//             searchQuery={teamsSettingModal.searchQuery}
//             searchPlaceholder={teamsSettingModal.searchPlaceholder}
//             // 保存関連
//             isSaving={teamsSettingModal.isSaving}
//             isSaveCompleted={teamsSettingModal.isSaveCompleted}
//             isSaveDisabled={teamsSettingModal.isSaveDisabled}
//             // イベントハンドラー
//             onTabChange={teamsSettingModal.handleTabChange}
//             onSearchQueryChange={teamsSettingModal.handleSearchQueryChange}
//             onItemToggle={teamsSettingModal.handleItemToggle}
//             onRemoveSelectedItem={teamsSettingModal.handleRemoveSelectedItem}
//             onKeyDown={teamsSettingModal.handleKeyDown}
//             onSave={teamsSettingModal.handleSave}
//           />
//         </div>
//       </div>

//       {/* トースターメッセージ */}
//       <MessageToaster
//         isActive={teamsSettingModal.isToasterShown}
//         messageType={teamsSettingModal.toasterMessage}
//       />
//     </div>
//   );
// };

// TeamsSettingModal.defaultProps = {
//   className: '',
//   open: false,
// };

// export default TeamsSettingModal;
