@import '../../../../styles/variables';

.teams-setting-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-guide-background-1);
}

.teams-setting-content-header {
  flex-shrink: 0;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid var(--color-guide-foreground-6);
  margin-bottom: 20px;
}

.teams-setting-content-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--color-guide-foreground-1);
  text-align: center;
}

.teams-setting-content-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;
  overflow: hidden;
}

.teams-setting-content-description {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: var(--color-guide-foreground-2);
  text-align: center;
  line-height: 1.4;
}

.teams-setting-content-tabs {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.teams-setting-content-search {
  flex-shrink: 0;
  margin-bottom: 16px;
}

.teams-setting-content-selected {
  flex-shrink: 0;
  margin-bottom: 16px;
  min-height: 0; // 選択アイテムがない場合の高さを0にする
}

.teams-setting-content-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-bottom: 20px;
}

.teams-setting-content-save {
  flex-shrink: 0;
  margin-top: auto;
}

/* ダークテーマ対応 */
@media (prefers-color-scheme: dark) {
  .teams-setting-content {
    background-color: var(--color-guide-background-2);
  }

  .teams-setting-content-header {
    border-color: var(--color-guide-foreground-5);
  }

  .teams-setting-content-title {
    color: var(--color-guide-foreground-1);
  }

  .teams-setting-content-description {
    color: var(--color-guide-foreground-2);
  }
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
  .teams-setting-content-header {
    padding: 16px 16px 0 16px;
    margin-bottom: 16px;
  }

  .teams-setting-content-title {
    font-size: 20px;
  }

  .teams-setting-content-main {
    padding: 0 16px 16px 16px;
  }

  .teams-setting-content-description {
    font-size: 13px;
    margin-bottom: 16px;
  }

  .teams-setting-content-tabs {
    margin-bottom: 12px;
  }

  .teams-setting-content-search {
    margin-bottom: 12px;
  }

  .teams-setting-content-selected {
    margin-bottom: 12px;
  }

  .teams-setting-content-items {
    margin-bottom: 16px;
  }
}

/* 小さな画面での調整 */
@media (max-width: 480px) {
  .teams-setting-content-header {
    padding: 12px 12px 0 12px;
    margin-bottom: 12px;
  }

  .teams-setting-content-title {
    font-size: 18px;
  }

  .teams-setting-content-main {
    padding: 0 12px 12px 12px;
  }

  .teams-setting-content-description {
    font-size: 12px;
    margin-bottom: 12px;
  }
}

/* 高さが制限された環境での調整 */
@media (max-height: 600px) {
  .teams-setting-content-header {
    padding: 12px 20px 0 20px;
    margin-bottom: 12px;
  }

  .teams-setting-content-title {
    font-size: 20px;
  }

  .teams-setting-content-description {
    margin-bottom: 12px;
  }

  .teams-setting-content-tabs {
    margin-bottom: 12px;
  }

  .teams-setting-content-search {
    margin-bottom: 12px;
  }

  .teams-setting-content-selected {
    margin-bottom: 12px;
  }

  .teams-setting-content-items {
    margin-bottom: 12px;
  }
}

/* アクセシビリティ対応 */
@media (prefers-reduced-motion: reduce) {
  .teams-setting-content * {
    transition: none !important;
    animation: none !important;
  }
}

/* ハイコントラストモード対応 */
@media (prefers-contrast: high) {
  .teams-setting-content-header {
    border-width: 2px;
    border-color: var(--color-guide-foreground-1);
  }

  .teams-setting-content-title {
    color: var(--color-guide-foreground-1);
    font-weight: 700;
  }

  .teams-setting-content-description {
    color: var(--color-guide-foreground-1);
    font-weight: 500;
  }
}

/* フォーカス管理 */
.teams-setting-content {
  &:focus-within {
    .teams-setting-content-title {
      color: var(--color-guide-brand-foreground);
    }
  }
}

/* スクロール対応 */
.teams-setting-content-main {
  overflow-y: auto;
  
  // Webkit系ブラウザ
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-guide-foreground-6);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-guide-foreground-4);
    border-radius: 3px;

    &:hover {
      background: var(--color-guide-foreground-3);
    }
  }

  // Firefox用
  scrollbar-width: thin;
  scrollbar-color: var(--color-guide-foreground-4) var(--color-guide-foreground-6);
}
