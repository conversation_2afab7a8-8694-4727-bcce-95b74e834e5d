import { renderHook } from '@testing-library/react-hooks';
import { openDB } from 'idb';
import { dbMock, clearIdbMocks } from '../../mocks/idb';
import { INDEX_DB_VERSION } from '../../types/IGeraniumAttaneDB';
import useIndexedDbAccessor from './useIndexedDbAccessor';
import { ISplitViewListSingle } from '../../components/domains/split-view/types/ISplitViewListSingle';

jest.mock('idb', () => ({
  openDB: jest.fn(),
}));

const upgradeDbMock = {
  deleteObjectStore: jest.fn(),
  createObjectStore: jest.fn(),
};

// openDBのmock
let oldVersionOnOpenDBUpgrade = 0;
type OpenDBUpgradeCallback = (db: typeof upgradeDbMock, version: number) => void;
const openDBMock = (openDB as jest.Mock).mockImplementation(
  (name: string, version: number, callbacks?: { upgrade: OpenDBUpgradeCallback }) => {
    if (version !== INDEX_DB_VERSION) {
      // DBバージョンの不整合を検知する
      return Promise.reject(new Error('DB_VERSION_MISMATCH'));
    }
    // upgradeを実行
    if (callbacks) {
      callbacks.upgrade(upgradeDbMock, oldVersionOnOpenDBUpgrade);
    }
    // dbMockをresolve
    return Promise.resolve(dbMock);
  },
);

describe('useIndexedDbAccessor', () => {
  async function getHook(initialEntries: ISplitViewListSingle[]) {
    dbMock.getAll.mockResolvedValue(initialEntries);
    const getHookResult = renderHook(
      () => useIndexedDbAccessor(),
    );
    const { result, waitForNextUpdate } = getHookResult;

    // 初期化前はundefined
    expect(result.current[0]).toBeUndefined();

    await waitForNextUpdate();

    // openDBが実行されている
    expect(openDBMock).toHaveBeenCalledTimes(1);
    expect(openDBMock).toHaveBeenCalledWith(
      'geranium-attane',
      INDEX_DB_VERSION,
      expect.objectContaining({
        upgrade: expect.anything(),
      }),
    );
    // closeが呼ばれている
    expect(dbMock.close).toHaveBeenCalledTimes(1);

    // 初期化後はundefinedではない
    expect(result.current[0]).not.toBeUndefined();

    openDBMock.mockClear();
    clearIdbMocks();

    return getHookResult;
  }

  beforeEach(() => {
    openDBMock.mockClear();
    clearIdbMocks();
    upgradeDbMock.deleteObjectStore.mockClear();
    upgradeDbMock.createObjectStore.mockClear();
  });

  describe('when initialized', () => {
    it('should call openDB', async () => {
      // getHook内部のexpectが評価される
      await getHook([]);
    });

    describe('when oldVersion is 0', () => {
      beforeEach(() => {
        oldVersionOnOpenDBUpgrade = 0;
      });

      it('should create stores', async () => {
        await getHook([]);
        // oldVersion < 1: bookmarks, bookmark_queue (2個)
        // oldVersion < 2: search_results_cache (1個)
        // oldVersion < 3: 3個削除 + bookmarks, bookmark_queue, search_results_cache (3個)
        // oldVersion < 4: teams_chats, teams_chats_queue (2個)
        // 合計: 2 + 1 + 3 + 2 = 8個
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledTimes(8);
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('bookmarks');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('bookmark_queue');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('search_results_cache');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats_queue');
      });
    });

    describe('when oldVersion is 1', () => {
      beforeEach(() => {
        oldVersionOnOpenDBUpgrade = 1;
      });

      it('should create stores', async () => {
        await getHook([]);
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledTimes(3);
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledWith('bookmarks');
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledWith('bookmark_queue');
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledWith('search_results_cache');

        // oldVersion < 2: search_results_cache (1個)
        // oldVersion < 3: 3個削除 + bookmarks, bookmark_queue, search_results_cache (3個)
        // oldVersion < 4: teams_chats, teams_chats_queue (2個)
        // 合計: 1 + 3 + 2 = 6個
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledTimes(6);
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('bookmarks');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('bookmark_queue');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('search_results_cache');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats_queue');
      });
    });

    describe('when oldVersion is 2', () => {
      beforeEach(() => {
        oldVersionOnOpenDBUpgrade = 2;
      });

      it('should create stores', async () => {
        await getHook([]);
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledTimes(3);
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledWith('bookmarks');
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledWith('bookmark_queue');
        expect(upgradeDbMock.deleteObjectStore).toHaveBeenCalledWith('search_results_cache');

        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledTimes(5);
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('bookmarks');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('bookmark_queue');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('search_results_cache');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats_queue');
      });
    });

    describe('when oldVersion is 3', () => {
      beforeEach(() => {
        oldVersionOnOpenDBUpgrade = 3;
      });

      it('should create teams_chats stores', async () => {
        await getHook([]);
        expect(upgradeDbMock.deleteObjectStore).not.toHaveBeenCalled();

        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledTimes(2);
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats');
        expect(upgradeDbMock.createObjectStore).toHaveBeenCalledWith('teams_chats_queue');
      });
    });
  });
});
