@import '../../../../styles/variables';

.teams-search-input {
  margin-bottom: 16px;
}

.teams-search-input-field {
  width: 100%;
  
  // 入力フィールドのスタイリング
  .ui-input__input {
    border-radius: 4px;
    border: 1px solid var(--color-guide-foreground-5);
    padding: 8px 12px;
    font-size: 14px;
    
    &:focus {
      border-color: var(--color-guide-brand-border);
      box-shadow: 0 0 0 1px var(--color-guide-brand-border);
    }
    
    &:disabled {
      background-color: var(--color-guide-foreground-6);
      color: var(--color-guide-foreground-3);
      cursor: not-allowed;
    }
  }
  
  // プレースホルダーのスタイリング
  .ui-input__input::placeholder {
    color: var(--color-guide-foreground-3);
    font-style: italic;
  }
}

/* ダークテーマ対応 */
@media (prefers-color-scheme: dark) {
  .teams-search-input-field {
    .ui-input__input {
      background-color: var(--color-guide-background-2);
      border-color: var(--color-guide-foreground-4);
      color: var(--color-guide-foreground-1);
      
      &:focus {
        border-color: var(--color-guide-brand-border);
        background-color: var(--color-guide-background-1);
      }
      
      &:disabled {
        background-color: var(--color-guide-foreground-5);
        color: var(--color-guide-foreground-4);
      }
    }
    
    .ui-input__input::placeholder {
      color: var(--color-guide-foreground-4);
    }
  }
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
  .teams-search-input {
    margin-bottom: 12px;
  }
  
  .teams-search-input-field {
    .ui-input__input {
      padding: 10px 12px;
      font-size: 16px; // モバイルでのズーム防止
    }
  }
}
