import '@testing-library/jest-dom';
import { renderHook, act } from '@testing-library/react-hooks';
import useTeamsSettingSelection, { UseTeamsSettingSelectionProps } from './useTeamsSettingSelection';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';
import { TeamsSettingTabType } from '../../components/domains/app-info/teamsSettingModal/TeamsSettingTabs';
import { ToasterMessage } from '../../components/commons/molecules/message-toaster/MessageToaster';

// テスト用のモックデータ
const createMockUserChatItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
  id: 'test-chat-id',
  name: 'テストチャット',
  type: 'チャット',
  chatType: 'oneOnOne',
  ...overrides,
});

const createMockChannelItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
  id: 'test-channel-id',
  name: 'テストチャネル',
  type: 'チャネル',
  chatType: 'channel',
  teamId: 'test-team-id',
  ...overrides,
});

describe('useTeamsSettingSelection', () => {
  // モック関数
  const extendPopupTimerMock = jest.fn();

  // テスト用データ
  const mockChatItems = [
    createMockUserChatItem({ id: 'chat-1', name: 'チャット1' }),
    createMockUserChatItem({ id: 'chat-2', name: 'チャット2' }),
    createMockChannelItem({ id: 'channel-1', name: 'チャネル1' }),
    createMockChannelItem({ id: 'channel-2', name: 'チャネル2' }),
  ];

  // デフォルトのprops
  const defaultProps: UseTeamsSettingSelectionProps = {
    allChatItems: mockChatItems,
    savedItems: new Set(),
    extendPopupTimer: extendPopupTimerMock,
    maxSelectionCount: 10,
  };

  beforeEach(() => {
    // 各テスト前にモックをクリア
    extendPopupTimerMock.mockClear();
  });

  describe('初期状態のテスト', () => {
    it('初期状態が正しく設定されている', () => {
      // 初期状態をテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      expect(result.current.selectedItems).toEqual(new Set());
      expect(result.current.activeTab).toBe(TeamsSettingTabType.CHAT);
      expect(result.current.searchQuery).toBe('');
      expect(result.current.chatCount).toBe(2);
      expect(result.current.channelCount).toBe(2);
      expect(result.current.searchPlaceholder).toBe('チャット名で検索');
    });

    it('保存済みアイテムが選択状態に反映される', () => {
      // 保存済みアイテムがある場合をテスト
      const savedItems = new Set(['chat-1', 'channel-1']);
      const { result } = renderHook(() => useTeamsSettingSelection({
        ...defaultProps,
        savedItems,
      }));

      expect(result.current.selectedItems).toEqual(savedItems);
    });
  });

  describe('フィルタリングのテスト', () => {
    it('初期状態ではチャットタブが選択されチャットのみ表示される', () => {
      // 初期状態でのフィルタリングをテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      expect(result.current.activeTab).toBe(TeamsSettingTabType.CHAT);
      expect(result.current.filteredChatItems).toHaveLength(2);
      expect(result.current.filteredChatItems.every(item => item.type === 'チャット')).toBe(true);
    });

    it('チャネルタブに切り替えるとチャネルのみ表示される', () => {
      // チャネルタブへの切り替えをテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      act(() => {
        result.current.handleTabChange(TeamsSettingTabType.CHANNEL);
      });

      expect(result.current.activeTab).toBe(TeamsSettingTabType.CHANNEL);
      expect(result.current.filteredChatItems).toHaveLength(2);
      expect(result.current.filteredChatItems.every(item => item.type === 'チャネル')).toBe(true);
      expect(result.current.searchPlaceholder).toBe('チャネル名で検索');
    });

    it('検索クエリでフィルタリングされる', () => {
      // 検索クエリでのフィルタリングをテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      act(() => {
        result.current.handleSearchQueryChange({} as React.SyntheticEvent, { value: 'チャット1' });
      });

      expect(result.current.searchQuery).toBe('チャット1');
      expect(result.current.filteredChatItems).toHaveLength(1);
      expect(result.current.filteredChatItems[0].name).toBe('チャット1');
    });

    it('タブ切り替え時に検索クエリがクリアされる', () => {
      // タブ切り替え時の検索クエリクリアをテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      // まず検索クエリを設定
      act(() => {
        result.current.handleSearchQueryChange({} as React.SyntheticEvent, { value: 'test' });
      });

      expect(result.current.searchQuery).toBe('test');

      // タブを切り替え
      act(() => {
        result.current.handleTabChange(TeamsSettingTabType.CHANNEL);
      });

      expect(result.current.searchQuery).toBe('');
    });
  });

  describe('アイテム選択のテスト', () => {
    it('アイテムを選択できる', () => {
      // アイテム選択をテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      act(() => {
        result.current.handleItemToggle('chat-1');
      });

      expect(result.current.selectedItems).toEqual(new Set(['chat-1']));
    });

    it('選択済みアイテムを選択解除できる', () => {
      // アイテム選択解除をテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      // まず選択
      act(() => {
        result.current.handleItemToggle('chat-1');
      });

      expect(result.current.selectedItems).toEqual(new Set(['chat-1']));

      // 選択解除
      act(() => {
        result.current.handleItemToggle('chat-1');
      });

      expect(result.current.selectedItems).toEqual(new Set());
    });

    it('選択上限に達した場合はトースターメッセージが表示される', () => {
      // 選択上限のテスト
      const { result } = renderHook(() => useTeamsSettingSelection({
        ...defaultProps,
        maxSelectionCount: 1,
      }));

      // 1つ目を選択
      act(() => {
        result.current.handleItemToggle('chat-1');
      });

      expect(result.current.selectedItems).toEqual(new Set(['chat-1']));

      // 2つ目を選択しようとする（上限に達している）
      act(() => {
        result.current.handleItemToggle('chat-2');
      });

      expect(result.current.selectedItems).toEqual(new Set(['chat-1'])); // 変更されない
      expect(extendPopupTimerMock).toHaveBeenCalledWith(ToasterMessage.MAX_TEAMS_SELECTION);
    });

    it('handleRemoveSelectedItemで選択を解除できる', () => {
      // 選択解除ハンドラーをテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      // まず選択
      act(() => {
        result.current.handleItemToggle('chat-1');
      });

      expect(result.current.selectedItems).toEqual(new Set(['chat-1']));

      // 削除ハンドラーで解除
      act(() => {
        result.current.handleRemoveSelectedItem('chat-1');
      });

      expect(result.current.selectedItems).toEqual(new Set());
    });
  });

  describe('キーボードイベントのテスト', () => {
    it('Enterキーでアイテムを選択できる', () => {
      // Enterキーでの選択をテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      const mockEvent = {
        key: 'Enter',
        preventDefault: jest.fn(),
      } as unknown as React.KeyboardEvent;

      act(() => {
        result.current.handleKeyDown(mockEvent, 'chat-1');
      });

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(result.current.selectedItems).toEqual(new Set(['chat-1']));
    });

    it('スペースキーでアイテムを選択できる', () => {
      // スペースキーでの選択をテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      const mockEvent = {
        key: ' ',
        preventDefault: jest.fn(),
      } as unknown as React.KeyboardEvent;

      act(() => {
        result.current.handleKeyDown(mockEvent, 'chat-1');
      });

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(result.current.selectedItems).toEqual(new Set(['chat-1']));
    });

    it('その他のキーでは何も起こらない', () => {
      // その他のキーでの動作をテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      const mockEvent = {
        key: 'a',
        preventDefault: jest.fn(),
      } as unknown as React.KeyboardEvent;

      act(() => {
        result.current.handleKeyDown(mockEvent, 'chat-1');
      });

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(result.current.selectedItems).toEqual(new Set());
    });
  });

  describe('リセット機能のテスト', () => {
    it('resetSelection関数で状態がリセットされる', () => {
      // リセット機能をテスト
      const { result } = renderHook(() => useTeamsSettingSelection(defaultProps));

      // 状態を変更
      act(() => {
        result.current.handleItemToggle('chat-1');
        result.current.handleTabChange(TeamsSettingTabType.CHANNEL);
        result.current.handleSearchQueryChange({} as React.SyntheticEvent, { value: 'test' });
      });

      expect(result.current.selectedItems).toEqual(new Set(['chat-1']));
      expect(result.current.activeTab).toBe(TeamsSettingTabType.CHANNEL);
      expect(result.current.searchQuery).toBe('test');

      // リセット
      act(() => {
        result.current.resetSelection();
      });

      expect(result.current.selectedItems).toEqual(new Set());
      expect(result.current.activeTab).toBe(TeamsSettingTabType.CHAT);
      expect(result.current.searchQuery).toBe('');
    });
  });
});
