import * as React from 'react';
import { UseTeamsChatsApiReturnType } from '../accessors/useTeamsChatsApiAccessor';
import useUserChatsAndChannelsAccessor from '../accessors/useUserChatsAndChannelsAccessor';
import useComponentInitUtility from '../utilities/useComponentInitUtility';
import useMessageToasterBehavior from '../behaviors/useMessageToasterBehavior';
import useTeamsSettingData from './useTeamsSettingData';
import useTeamsSettingSelection from './useTeamsSettingSelection';

export interface UseTeamsSettingModalProps {
  open: boolean;
  onClose: () => void;
  useTeamsChatsApiAccessorReturn: UseTeamsChatsApiReturnType;
}

export interface UseTeamsSettingModalReturnType {
  // データ関連
  allChatItems: ReturnType<typeof useTeamsSettingData>['allChatItems'];
  savedItems: ReturnType<typeof useTeamsSettingData>['savedItems'];
  isLoadingSavedItems: ReturnType<typeof useTeamsSettingData>['isLoadingSavedItems'];
  isLoading: boolean;
  error: string | null;

  // 選択関連
  selectedItems: ReturnType<typeof useTeamsSettingSelection>['selectedItems'];
  activeTab: ReturnType<typeof useTeamsSettingSelection>['activeTab'];
  searchQuery: ReturnType<typeof useTeamsSettingSelection>['searchQuery'];
  filteredChatItems: ReturnType<typeof useTeamsSettingSelection>['filteredChatItems'];
  chatCount: ReturnType<typeof useTeamsSettingSelection>['chatCount'];
  channelCount: ReturnType<typeof useTeamsSettingSelection>['channelCount'];
  searchPlaceholder: ReturnType<typeof useTeamsSettingSelection>['searchPlaceholder'];

  // 保存状態関連
  isSaving: boolean;
  isSaveCompleted: boolean;
  saveButtonContent: React.ReactNode;
  isSaveDisabled: boolean;

  // トースター関連
  isToasterShown: boolean;
  toasterMessage: ReturnType<typeof useMessageToasterBehavior>[1];

  // イベントハンドラー
  handleItemToggle: ReturnType<typeof useTeamsSettingSelection>['handleItemToggle'];
  handleRemoveSelectedItem: ReturnType<typeof useTeamsSettingSelection>['handleRemoveSelectedItem'];
  handleKeyDown: ReturnType<typeof useTeamsSettingSelection>['handleKeyDown'];
  handleTabChange: ReturnType<typeof useTeamsSettingSelection>['handleTabChange'];
  handleSearchQueryChange: ReturnType<typeof useTeamsSettingSelection>['handleSearchQueryChange'];
  handleClose: () => void;
  handleSave: () => Promise<void>;

  // 統合されたローディング状態
  isLoadingData: boolean;
}

/**
 * Teams設定モーダルのメイン状態管理とビジネスロジックを管理するカスタムフック
 */
const useTeamsSettingModal = (props: UseTeamsSettingModalProps): UseTeamsSettingModalReturnType => {
  const {
    open,
    onClose,
    useTeamsChatsApiAccessorReturn,
  } = props;

  const {
    postTeamsChatsApi,
    getTeamsChatsApi,
    deleteTeamsChatsApi,
  } = useTeamsChatsApiAccessorReturn;

  // 保存状態管理
  const [isSaving, setIsSaving] = React.useState(false);
  const [isSaveCompleted, setIsSaveCompleted] = React.useState(false);

  // コンポーネント初期化ユーティリティ
  const [, , callbacks] = useComponentInitUtility({
    componentName: 'TeamsSettingModal',
  });

  const tokenProvider = React.useMemo(() => {
    if (!callbacks?.get) return undefined;
    const graphTokenProvider = callbacks.get('graph');
    return graphTokenProvider ? () => graphTokenProvider() : undefined;
  }, [callbacks]);

  // APIアクセサーを初期化
  const {
    fetchUserChatsAndChannels,
    isLoading,
    error,
  } = useUserChatsAndChannelsAccessor(tokenProvider);

  // トースター機能
  const [isToasterShown, toasterMessage, extendPopupTimer] = useMessageToasterBehavior(3000);

  // データ管理フック
  const dataHook = useTeamsSettingData({
    fetchUserChatsAndChannels,
    getTeamsChatsApi,
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isModalOpen: open,
  });

  // 選択管理フック
  const selectionHook = useTeamsSettingSelection({
    allChatItems: dataHook.allChatItems,
    savedItems: dataHook.savedItems,
    extendPopupTimer,
    maxSelectionCount: 10,
  });

  // 統合されたローディング状態
  const isLoadingData = isLoading || dataHook.isLoadingSavedItems;

  // 保存ボタンのコンテンツ
  const saveButtonContent = React.useMemo(() => {
    if (isSaving) {
      return '保存中...';
    }
    if (isSaveCompleted) {
      return '保存完了';
    }
    return '保存';
  }, [isSaving, isSaveCompleted]);

  // 保存ボタンの無効化条件
  const isSaveDisabled = React.useMemo(() => !postTeamsChatsApi
      || !deleteTeamsChatsApi
      || isSaving
      || isSaveCompleted
      || (selectionHook.selectedItems.size === 0 && dataHook.savedItems.size === 0)
      || (selectionHook.selectedItems.size === dataHook.savedItems.size
          && Array.from(selectionHook.selectedItems).every((id) => dataHook.savedItems.has(id))), [
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    isSaving,
    isSaveCompleted,
    selectionHook.selectedItems,
    dataHook.savedItems,
  ]);

  // モーダルを閉じるハンドラー
  const handleClose = React.useCallback(() => {
    // 状態をリセット
    selectionHook.resetSelection();
    setIsSaving(false);
    setIsSaveCompleted(false);

    if (onClose) onClose();
  }, [onClose, selectionHook]);

  // 保存ハンドラー
  const handleSave = React.useCallback(async () => {
    try {
      // ローディング状態を開始
      setIsSaving(true);

      await dataHook.saveSelectedItems(selectionHook.selectedItems, dataHook.allChatItems);

      // 保存完了状態を表示
      setIsSaving(false);
      setIsSaveCompleted(true);

      // 0.8秒後にモーダルを閉じる
      setTimeout(() => {
        handleClose();
      }, 800);
    } catch (saveError) {
      // エラーが発生した場合
      setIsSaving(false);
      throw new Error(`保存処理でエラーが発生しました: ${saveError}`);
    }
  }, [
    dataHook,
    selectionHook,
    handleClose,
  ]);

  return {
    // データ関連
    allChatItems: dataHook.allChatItems,
    savedItems: dataHook.savedItems,
    isLoadingSavedItems: dataHook.isLoadingSavedItems,
    isLoading,
    error,

    // 選択関連
    selectedItems: selectionHook.selectedItems,
    activeTab: selectionHook.activeTab,
    searchQuery: selectionHook.searchQuery,
    filteredChatItems: selectionHook.filteredChatItems,
    chatCount: selectionHook.chatCount,
    channelCount: selectionHook.channelCount,
    searchPlaceholder: selectionHook.searchPlaceholder,

    // 保存状態関連
    isSaving,
    isSaveCompleted,
    saveButtonContent,
    isSaveDisabled,

    // トースター関連
    isToasterShown,
    toasterMessage,

    // イベントハンドラー
    handleItemToggle: selectionHook.handleItemToggle,
    handleRemoveSelectedItem: selectionHook.handleRemoveSelectedItem,
    handleKeyDown: selectionHook.handleKeyDown,
    handleTabChange: selectionHook.handleTabChange,
    handleSearchQueryChange: selectionHook.handleSearchQueryChange,
    handleClose,
    handleSave,

    // 統合されたローディング状態
    isLoadingData,
  };
};

export default useTeamsSettingModal;
