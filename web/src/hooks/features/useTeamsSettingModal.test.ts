import '@testing-library/jest-dom';
import { renderHook, act } from '@testing-library/react-hooks';
import useTeamsSettingModal, { UseTeamsSettingModalProps } from './useTeamsSettingModal';
import { UseTeamsChatsApiReturnType } from '../accessors/useTeamsChatsApiAccessor';

import useTeamsSettingData from './useTeamsSettingData';
import useTeamsSettingSelection from './useTeamsSettingSelection';

// environment をモック
jest.mock('../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

// 依存関係をモック
jest.mock('../utilities/useComponentInitUtility', () => ({
  __esModule: true,
  default: jest.fn(() => [
    undefined,
    [],
    {
      get: jest.fn(() => () => Promise.resolve('mock-token')),
    },
  ]),
}));

jest.mock('../accessors/useUserChatsAndChannelsAccessor', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    fetchUserChatsAndChannels: jest.fn(),
    isLoading: false,
    error: null,
  })),
}));

jest.mock('../behaviors/useMessageToasterBehavior', () => ({
  __esModule: true,
  default: jest.fn(() => [false, 'BOOKMARK_ADDED', jest.fn()]),
}));

jest.mock('./useTeamsSettingData', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    allChatItems: [],
    savedItems: new Set(),
    isLoadingSavedItems: false,
    loadSavedItems: jest.fn(),
    saveSelectedItems: jest.fn(),
    setAllChatItems: jest.fn(),
    setSavedItems: jest.fn(),
  })),
}));

jest.mock('./useTeamsSettingSelection', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    selectedItems: new Set(),
    activeTab: 'チャット',
    searchQuery: '',
    filteredChatItems: [],
    chatCount: 0,
    channelCount: 0,
    searchPlaceholder: 'チャット名で検索',
    handleItemToggle: jest.fn(),
    handleRemoveSelectedItem: jest.fn(),
    handleKeyDown: jest.fn(),
    handleTabChange: jest.fn(),
    handleSearchQueryChange: jest.fn(),
    setSelectedItems: jest.fn(),
    resetSelection: jest.fn(),
  })),
}));

const useTeamsSettingDataMock = useTeamsSettingData as jest.MockedFunction<
typeof useTeamsSettingData>;
const useTeamsSettingSelectionMock = useTeamsSettingSelection as jest.MockedFunction<
typeof useTeamsSettingSelection>;

describe('useTeamsSettingModal', () => {
  // モック関数
  const onCloseMock = jest.fn();
  const postTeamsChatsApiMock = jest.fn();
  const getTeamsChatsApiMock = jest.fn();
  const deleteTeamsChatsApiMock = jest.fn();
  const saveSelectedItemsMock = jest.fn();
  const resetSelectionMock = jest.fn();

  // デフォルトのprops
  const defaultProps: UseTeamsSettingModalProps = {
    open: false,
    onClose: onCloseMock,
    useTeamsChatsApiAccessorReturn: {
      postTeamsChatsApi: postTeamsChatsApiMock,
      getTeamsChatsApi: getTeamsChatsApiMock,
      deleteTeamsChatsApi: deleteTeamsChatsApiMock,
    } as UseTeamsChatsApiReturnType,
  };

  beforeEach(() => {
    // 各テスト前にモックをクリア
    onCloseMock.mockClear();
    postTeamsChatsApiMock.mockClear();
    getTeamsChatsApiMock.mockClear();
    deleteTeamsChatsApiMock.mockClear();
    saveSelectedItemsMock.mockClear();
    resetSelectionMock.mockClear();

    // デフォルトのモック戻り値を設定
    useTeamsSettingDataMock.mockReturnValue({
      allChatItems: [],
      savedItems: new Set(),
      isLoadingSavedItems: false,
      loadSavedItems: jest.fn(),
      saveSelectedItems: saveSelectedItemsMock,
      setAllChatItems: jest.fn(),
      setSavedItems: jest.fn(),
    });

    useTeamsSettingSelectionMock.mockReturnValue({
      selectedItems: new Set(),
      activeTab: 'チャット',
      searchQuery: '',
      filteredChatItems: [],
      chatCount: 0,
      channelCount: 0,
      searchPlaceholder: 'チャット名で検索',
      handleItemToggle: jest.fn(),
      handleRemoveSelectedItem: jest.fn(),
      handleKeyDown: jest.fn(),
      handleTabChange: jest.fn(),
      handleSearchQueryChange: jest.fn(),
      setSelectedItems: jest.fn(),
      resetSelection: resetSelectionMock,
    });
  });

  describe('初期状態のテスト', () => {
    it('初期状態が正しく設定されている', () => {
      // 初期状態をテスト
      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      expect(result.current.isSaving).toBe(false);
      expect(result.current.isSaveCompleted).toBe(false);
      expect(result.current.saveButtonContent).toBe('保存');
      expect(result.current.isLoadingData).toBe(false);
    });

    it('保存ボタンの無効化条件が正しく動作する', () => {
      // 保存ボタンの無効化条件をテスト
      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      // 初期状態では無効化されている（選択アイテムも保存済みアイテムもない）
      expect(result.current.isSaveDisabled).toBe(true);
    });
  });

  describe('保存状態のテスト', () => {
    it('保存中の状態が正しく表示される', () => {
      // 保存中の状態をテスト
      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      // 保存処理を開始
      act(() => {
        result.current.handleSave();
      });

      expect(result.current.isSaving).toBe(true);
      expect(result.current.saveButtonContent).toContain('保存中...');
    });

    it('保存完了の状態が正しく表示される', async () => {
      // 保存完了の状態をテスト
      saveSelectedItemsMock.mockResolvedValue(undefined);

      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      await act(async () => {
        await result.current.handleSave();
      });

      expect(result.current.isSaving).toBe(false);
      expect(result.current.isSaveCompleted).toBe(true);
      expect(result.current.saveButtonContent).toBe('保存完了');
    });

    it('保存処理でエラーが発生した場合の処理', async () => {
      // 保存処理でのエラーハンドリングをテスト
      const errorMessage = 'Save Error';
      saveSelectedItemsMock.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      await act(async () => {
        await expect(result.current.handleSave()).rejects.toThrow('保存処理でエラーが発生しました');
      });

      expect(result.current.isSaving).toBe(false);
      expect(result.current.isSaveCompleted).toBe(false);
    });
  });

  describe('モーダル閉じる処理のテスト', () => {
    it('handleCloseが正しく動作する', () => {
      // モーダルを閉じる処理をテスト
      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      act(() => {
        result.current.handleClose();
      });

      expect(resetSelectionMock).toHaveBeenCalledTimes(1);
      expect(onCloseMock).toHaveBeenCalledTimes(1);
      expect(result.current.isSaving).toBe(false);
      expect(result.current.isSaveCompleted).toBe(false);
    });
  });

  describe('統合されたローディング状態のテスト', () => {
    it('isLoadingDataが正しく計算される', () => {
      // ローディング状態の統合をテスト
      useTeamsSettingDataMock.mockReturnValue({
        allChatItems: [],
        savedItems: new Set(),
        isLoadingSavedItems: true, // ローディング中
        loadSavedItems: jest.fn(),
        saveSelectedItems: saveSelectedItemsMock,
        setAllChatItems: jest.fn(),
        setSavedItems: jest.fn(),
      });

      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      expect(result.current.isLoadingData).toBe(true);
    });
  });

  describe('保存ボタン無効化条件の詳細テスト', () => {
    it('API関数が未定義の場合は無効化される', () => {
      // API関数が未定義の場合をテスト
      const { result } = renderHook(() => useTeamsSettingModal({
        ...defaultProps,
        useTeamsChatsApiAccessorReturn: {
          postTeamsChatsApi: undefined,
          deleteTeamsChatsApi: undefined,
          getTeamsChatsApi: getTeamsChatsApiMock,
        } as UseTeamsChatsApiReturnType,
      }));

      expect(result.current.isSaveDisabled).toBe(true);
    });

    it('選択アイテムがある場合は有効化される', () => {
      // 選択アイテムがある場合をテスト
      useTeamsSettingSelectionMock.mockReturnValue({
        selectedItems: new Set(['item-1']),
        activeTab: 'チャット',
        searchQuery: '',
        filteredChatItems: [],
        chatCount: 0,
        channelCount: 0,
        searchPlaceholder: 'チャット名で検索',
        handleItemToggle: jest.fn(),
        handleRemoveSelectedItem: jest.fn(),
        handleKeyDown: jest.fn(),
        handleTabChange: jest.fn(),
        handleSearchQueryChange: jest.fn(),
        setSelectedItems: jest.fn(),
        resetSelection: resetSelectionMock,
      });

      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      expect(result.current.isSaveDisabled).toBe(false);
    });

    it('選択アイテムと保存済みアイテムが同じ場合は無効化される', () => {
      // 選択アイテムと保存済みアイテムが同じ場合をテスト
      const sameItems = new Set(['item-1']);

      useTeamsSettingDataMock.mockReturnValue({
        allChatItems: [],
        savedItems: sameItems,
        isLoadingSavedItems: false,
        loadSavedItems: jest.fn(),
        saveSelectedItems: saveSelectedItemsMock,
        setAllChatItems: jest.fn(),
        setSavedItems: jest.fn(),
      });

      useTeamsSettingSelectionMock.mockReturnValue({
        selectedItems: sameItems,
        activeTab: 'チャット',
        searchQuery: '',
        filteredChatItems: [],
        chatCount: 0,
        channelCount: 0,
        searchPlaceholder: 'チャット名で検索',
        handleItemToggle: jest.fn(),
        handleRemoveSelectedItem: jest.fn(),
        handleKeyDown: jest.fn(),
        handleTabChange: jest.fn(),
        handleSearchQueryChange: jest.fn(),
        setSelectedItems: jest.fn(),
        resetSelection: resetSelectionMock,
      });

      const { result } = renderHook(() => useTeamsSettingModal(defaultProps));

      expect(result.current.isSaveDisabled).toBe(true);
    });
  });
});
