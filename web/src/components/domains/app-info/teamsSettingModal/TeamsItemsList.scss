@import '../../../../styles/variables';

.teams-items-list {
  margin-bottom: 20px;
}

.teams-items-list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--color-guide-foreground-5);
  border-radius: 4px;
  background-color: var(--color-guide-background-1);
}

.teams-items-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-guide-foreground-6);
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: var(--color-guide-foreground-6);
  }

  &:focus {
    outline: 2px solid var(--color-guide-brand-border);
    outline-offset: -2px;
  }

  &.selected {
    background-color: var(--color-guide-brand-background-light);
    border-color: var(--color-guide-brand-border);

    &:hover {
      background-color: var(--color-guide-brand-background-light);
    }
  }
}

.teams-items-list-item-content {
  flex: 1;
  min-width: 0;
}

.teams-items-list-item-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-guide-foreground-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  .teams-items-list-item.selected & {
    color: var(--color-guide-brand-foreground);
  }
}

.teams-items-list-item-icon {
  flex-shrink: 0;
  margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.teams-items-list-item-icon-selected,
.teams-items-list-item-icon-unselected {
  transition: transform 0.2s ease, color 0.2s ease;
}

.teams-items-list-item:hover .teams-items-list-item-icon-unselected {
  color: var(--color-guide-foreground-1);
  transform: scale(1.05);
}

.teams-items-list-item:hover .teams-items-list-item-icon-selected {
  transform: scale(1.15);
}

// 状態表示用のスタイル
.teams-items-list-loading,
.teams-items-list-error,
.teams-items-list-no-results {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-guide-foreground-2);
  font-size: 14px;
  border: 1px solid var(--color-guide-foreground-5);
  border-radius: 4px;
  background-color: var(--color-guide-background-1);
}

.teams-items-list-error {
  color: var(--color-guide-error-foreground);
  background-color: var(--color-guide-error-background-light);
  border-color: var(--color-guide-error-border);
}

.teams-items-list-loading {
  background-color: var(--color-guide-foreground-6);
}

/* ダークテーマ対応 */
@media (prefers-color-scheme: dark) {
  .teams-items-list-container {
    background-color: var(--color-guide-background-2);
    border-color: var(--color-guide-foreground-4);
  }

  .teams-items-list-item {
    border-color: var(--color-guide-foreground-5);

    &:hover {
      background-color: var(--color-guide-foreground-5);
    }

    &.selected {
      background-color: var(--color-guide-brand-background-dark);

      &:hover {
        background-color: var(--color-guide-brand-background-dark);
      }
    }
  }

  .teams-items-list-item-name {
    color: var(--color-guide-foreground-1);

    .teams-items-list-item.selected & {
      color: var(--color-guide-brand-foreground-light);
    }
  }

  .teams-items-list-loading,
  .teams-items-list-no-results {
    background-color: var(--color-guide-background-2);
    border-color: var(--color-guide-foreground-4);
    color: var(--color-guide-foreground-2);
  }

  .teams-items-list-error {
    background-color: var(--color-guide-error-background-dark);
    border-color: var(--color-guide-error-border-dark);
    color: var(--color-guide-error-foreground-light);
  }

  .teams-items-list-loading {
    background-color: var(--color-guide-foreground-5);
  }
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
  .teams-items-list-item {
    padding: 14px 12px;
  }

  .teams-items-list-item-name {
    font-size: 16px;
  }

  .teams-items-list-item-icon {
    width: 28px;
    height: 28px;
    margin-left: 8px;
  }

  .teams-items-list-container {
    max-height: 250px;
  }
}

/* スクロールバーのスタイリング */
.teams-items-list-container {
  // Webkit系ブラウザ
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-guide-foreground-6);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-guide-foreground-4);
    border-radius: 3px;

    &:hover {
      background: var(--color-guide-foreground-3);
    }
  }

  // Firefox用
  scrollbar-width: thin;
  scrollbar-color: var(--color-guide-foreground-4) var(--color-guide-foreground-6);
}
