import * as React from 'react';
import '@testing-library/jest-dom';
import { render, fireEvent, screen, waitFor } from '@testing-library/react';
import TeamsSaveButton, { ITeamsSaveButtonProps } from './TeamsSaveButton';

// environment をモック
jest.mock('../../../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

describe('TeamsSaveButton', () => {
  // モック関数
  const onClickMock = jest.fn();

  // デフォルトのprops
  const defaultProps: ITeamsSaveButtonProps = {
    isSaving: false,
    isSaveCompleted: false,
    isDisabled: false,
    onClick: onClickMock,
  };

  beforeEach(() => {
    // 各テスト前にモックをクリア
    onClickMock.mockClear();
  });

  describe('基本的なレンダリングのテスト', () => {
    it('通常状態でボタンが正常にレンダリングされる', () => {
      // 通常状態のレンダリングをテスト
      render(<TeamsSaveButton {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('保存');
      expect(button).not.toBeDisabled();
    });

    it('カスタムクラス名が適用される', () => {
      // カスタムクラス名の適用をテスト
      const customClassName = 'custom-save-button';
      const { container } = render(
        <TeamsSaveButton {...defaultProps} className={customClassName} />
      );

      const saveButtonContainer = container.querySelector('.teams-save-button');
      expect(saveButtonContainer).toHaveClass(customClassName);
    });
  });

  describe('保存状態の表示テスト', () => {
    it('保存中状態では適切なコンテンツが表示される', () => {
      // 保存中状態をテスト
      render(<TeamsSaveButton {...defaultProps} isSaving={true} />);

      const button = screen.getByRole('button');
      expect(button).toHaveTextContent('保存中...');
      expect(button).toHaveClass('saving');
    });

    it('保存完了状態では適切なコンテンツが表示される', () => {
      // 保存完了状態をテスト
      render(<TeamsSaveButton {...defaultProps} isSaveCompleted={true} />);

      const button = screen.getByRole('button');
      expect(button).toHaveTextContent('保存完了');
      expect(button).toHaveClass('completed');
    });

    it('保存中状態ではローダーが表示される', () => {
      // ローダーの表示をテスト
      render(<TeamsSaveButton {...defaultProps} isSaving={true} />);

      // FluentUIのLoaderコンポーネントが含まれていることを確認
      const button = screen.getByRole('button');
      expect(button.querySelector('.ui-loader')).toBeInTheDocument();
    });
  });

  describe('無効化状態のテスト', () => {
    it('無効化状態ではボタンが無効になる', () => {
      // 無効化状態をテスト
      render(<TeamsSaveButton {...defaultProps} isDisabled={true} />);

      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
    });

    it('保存中状態では実質的に無効化される', () => {
      // 保存中の無効化をテスト
      render(<TeamsSaveButton {...defaultProps} isSaving={true} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(onClickMock).not.toHaveBeenCalled();
    });

    it('保存完了状態では実質的に無効化される', () => {
      // 保存完了時の無効化をテスト
      render(<TeamsSaveButton {...defaultProps} isSaveCompleted={true} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(onClickMock).not.toHaveBeenCalled();
    });
  });

  describe('クリックイベントのテスト', () => {
    it('通常状態でクリック時にonClickが呼ばれる', async () => {
      // 通常クリックをテスト
      onClickMock.mockResolvedValue(undefined);
      render(<TeamsSaveButton {...defaultProps} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(onClickMock).toHaveBeenCalledTimes(1);
    });

    it('無効化状態ではクリックイベントが発生しない', () => {
      // 無効化状態でのクリックをテスト
      render(<TeamsSaveButton {...defaultProps} isDisabled={true} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(onClickMock).not.toHaveBeenCalled();
    });

    it('onClick関数でエラーが発生してもコンポーネントがクラッシュしない', async () => {
      // エラーハンドリングをテスト
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      onClickMock.mockRejectedValue(new Error('テストエラー'));

      render(<TeamsSaveButton {...defaultProps} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          '保存処理でエラーが発生しました:',
          expect.any(Error)
        );
      });

      consoleErrorSpy.mockRestore();
    });

    it('複数回クリックしても1回のみonClickが呼ばれる（保存中の場合）', () => {
      // 重複クリック防止をテスト
      render(<TeamsSaveButton {...defaultProps} isSaving={true} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      expect(onClickMock).not.toHaveBeenCalled();
    });
  });

  describe('スタイルとクラスのテスト', () => {
    it('通常状態では適切なクラスが設定される', () => {
      // 通常状態のクラスをテスト
      render(<TeamsSaveButton {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('teams-save-button-element');
      expect(button).not.toHaveClass('saving');
      expect(button).not.toHaveClass('completed');
    });

    it('保存中状態では適切なクラスが設定される', () => {
      // 保存中状態のクラスをテスト
      render(<TeamsSaveButton {...defaultProps} isSaving={true} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('teams-save-button-element');
      expect(button).toHaveClass('saving');
      expect(button).not.toHaveClass('completed');
    });

    it('保存完了状態では適切なクラスが設定される', () => {
      // 保存完了状態のクラスをテスト
      render(<TeamsSaveButton {...defaultProps} isSaveCompleted={true} />);

      const button = screen.getByRole('button');
      expect(button).toHaveClass('teams-save-button-element');
      expect(button).not.toHaveClass('saving');
      expect(button).toHaveClass('completed');
    });

    it('保存完了状態では特別なスタイルが適用される', () => {
      // 保存完了時のスタイルをテスト
      render(<TeamsSaveButton {...defaultProps} isSaveCompleted={true} />);

      const button = screen.getByRole('button');
      // FluentUIのButton内部のスタイル適用を確認するのは困難なため、
      // クラスの存在のみを確認
      expect(button).toHaveClass('completed');
    });
  });

  describe('アクセシビリティのテスト', () => {
    it('適切なroleが設定されている', () => {
      // roleの設定をテスト
      render(<TeamsSaveButton {...defaultProps} />);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
    });

    it('ボタンにフォーカスできる', () => {
      // フォーカスのテスト
      render(<TeamsSaveButton {...defaultProps} />);

      const button = screen.getByRole('button');
      button.focus();

      expect(button).toHaveFocus();
    });

    it('無効化状態ではフォーカスできない', () => {
      // 無効化状態でのフォーカステスト
      render(<TeamsSaveButton {...defaultProps} isDisabled={true} />);

      const button = screen.getByRole('button');
      button.focus();

      expect(button).not.toHaveFocus();
    });

    it('キーボードでの操作が可能', () => {
      // キーボード操作をテスト
      onClickMock.mockResolvedValue(undefined);
      render(<TeamsSaveButton {...defaultProps} />);

      const button = screen.getByRole('button');
      button.focus();
      fireEvent.keyDown(button, { key: 'Enter' });

      expect(onClickMock).toHaveBeenCalledTimes(1);
    });
  });

  describe('状態遷移のテスト', () => {
    it('通常 → 保存中 → 保存完了の状態遷移が正しく表示される', () => {
      // 状態遷移をテスト
      const { rerender } = render(<TeamsSaveButton {...defaultProps} />);

      // 通常状態
      let button = screen.getByRole('button');
      expect(button).toHaveTextContent('保存');
      expect(button).not.toHaveClass('saving');
      expect(button).not.toHaveClass('completed');

      // 保存中状態
      rerender(<TeamsSaveButton {...defaultProps} isSaving={true} />);
      button = screen.getByRole('button');
      expect(button).toHaveTextContent('保存中...');
      expect(button).toHaveClass('saving');
      expect(button).not.toHaveClass('completed');

      // 保存完了状態
      rerender(<TeamsSaveButton {...defaultProps} isSaving={false} isSaveCompleted={true} />);
      button = screen.getByRole('button');
      expect(button).toHaveTextContent('保存完了');
      expect(button).not.toHaveClass('saving');
      expect(button).toHaveClass('completed');
    });
  });
});
