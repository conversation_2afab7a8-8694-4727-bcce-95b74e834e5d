import * as React from 'react';
import '@testing-library/jest-dom';
import { render, fireEvent, screen } from '@testing-library/react';
import TeamsSearchInput, { ITeamsSearchInputProps } from './TeamsSearchInput';

// environment をモック
jest.mock('../../../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

describe('TeamsSearchInput', () => {
  // モック関数
  const onChangeMock = jest.fn();

  // デフォルトのprops
  const defaultProps: ITeamsSearchInputProps = {
    placeholder: 'テスト用プレースホルダー',
    value: '',
    onChange: onChangeMock,
  };

  beforeEach(() => {
    // 各テスト前にモックをクリア
    onChangeMock.mockClear();
  });

  describe('基本的なレンダリングのテスト', () => {
    it('コンポーネントが正常にレンダリングされる', () => {
      // 基本的なレンダリングをテスト
      render(<TeamsSearchInput {...defaultProps} />);

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('placeholder', 'テスト用プレースホルダー');
    });

    it('valueプロパティが正しく反映される', () => {
      // valueプロパティの反映をテスト
      const testValue = 'テスト入力値';
      render(<TeamsSearchInput {...defaultProps} value={testValue} />);

      const input = screen.getByRole('textbox');
      expect(input).toHaveValue(testValue);
    });

    it('placeholderプロパティが正しく反映される', () => {
      // placeholderプロパティの反映をテスト
      const testPlaceholder = 'カスタムプレースホルダー';
      render(<TeamsSearchInput {...defaultProps} placeholder={testPlaceholder} />);

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', testPlaceholder);
    });

    it('カスタムクラス名が適用される', () => {
      // カスタムクラス名の適用をテスト
      const customClassName = 'custom-search-input';
      const { container } = render(
        <TeamsSearchInput {...defaultProps} className={customClassName} />
      );

      const searchInputContainer = container.querySelector('.teams-search-input');
      expect(searchInputContainer).toHaveClass(customClassName);
    });
  });

  describe('入力イベントのテスト', () => {
    it('入力時にonChangeが呼ばれる', () => {
      // 入力イベントのテスト
      render(<TeamsSearchInput {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const testInput = 'テスト入力';

      fireEvent.change(input, { target: { value: testInput } });

      expect(onChangeMock).toHaveBeenCalledTimes(1);
      // onChangeの引数の詳細な検証は、実際のFluentUIの実装に依存するため、
      // 呼び出し回数のみをテスト
    });

    it('複数回の入力でonChangeが複数回呼ばれる', () => {
      // 複数回の入力イベントをテスト
      render(<TeamsSearchInput {...defaultProps} />);

      const input = screen.getByRole('textbox');

      fireEvent.change(input, { target: { value: 'a' } });
      fireEvent.change(input, { target: { value: 'ab' } });
      fireEvent.change(input, { target: { value: 'abc' } });

      expect(onChangeMock).toHaveBeenCalledTimes(3);
    });

    it('空文字の入力でもonChangeが呼ばれる', () => {
      // 空文字の入力をテスト
      render(<TeamsSearchInput {...defaultProps} value="初期値" />);

      const input = screen.getByRole('textbox');

      fireEvent.change(input, { target: { value: '' } });

      expect(onChangeMock).toHaveBeenCalledTimes(1);
    });
  });

  describe('無効化状態のテスト', () => {
    it('disabledがtrueの場合、入力フィールドが無効化される', () => {
      // 無効化状態をテスト
      render(<TeamsSearchInput {...defaultProps} disabled={true} />);

      const input = screen.getByRole('textbox');
      expect(input).toBeDisabled();
    });

    it('disabledがfalseの場合、入力フィールドが有効である', () => {
      // 有効状態をテスト
      render(<TeamsSearchInput {...defaultProps} disabled={false} />);

      const input = screen.getByRole('textbox');
      expect(input).not.toBeDisabled();
    });

    it('disabledが未指定の場合、入力フィールドが有効である', () => {
      // デフォルト状態をテスト
      render(<TeamsSearchInput {...defaultProps} />);

      const input = screen.getByRole('textbox');
      expect(input).not.toBeDisabled();
    });

    it('無効化状態では入力イベントが発生しない', () => {
      // 無効化状態での入力イベントをテスト
      render(<TeamsSearchInput {...defaultProps} disabled={true} />);

      const input = screen.getByRole('textbox');

      fireEvent.change(input, { target: { value: 'テスト' } });

      // 無効化されているため、onChangeは呼ばれない
      expect(onChangeMock).not.toHaveBeenCalled();
    });
  });

  describe('アクセシビリティのテスト', () => {
    it('入力フィールドにフォーカスできる', () => {
      // フォーカスのテスト
      render(<TeamsSearchInput {...defaultProps} />);

      const input = screen.getByRole('textbox');
      input.focus();

      expect(input).toHaveFocus();
    });

    it('無効化状態ではフォーカスできない', () => {
      // 無効化状態でのフォーカステスト
      render(<TeamsSearchInput {...defaultProps} disabled={true} />);

      const input = screen.getByRole('textbox');
      input.focus();

      expect(input).not.toHaveFocus();
    });

    it('適切なroleが設定されている', () => {
      // roleの設定をテスト
      render(<TeamsSearchInput {...defaultProps} />);

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });
  });

  describe('プレースホルダーの動的変更テスト', () => {
    it('プレースホルダーが動的に変更される', () => {
      // プレースホルダーの動的変更をテスト
      const { rerender } = render(
        <TeamsSearchInput {...defaultProps} placeholder="初期プレースホルダー" />
      );

      let input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', '初期プレースホルダー');

      // プレースホルダーを変更
      rerender(
        <TeamsSearchInput {...defaultProps} placeholder="変更後プレースホルダー" />
      );

      input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', '変更後プレースホルダー');
    });

    it('チャットタブ用のプレースホルダーが正しく表示される', () => {
      // チャットタブ用プレースホルダーをテスト
      render(<TeamsSearchInput {...defaultProps} placeholder="チャット名で検索" />);

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', 'チャット名で検索');
    });

    it('チャネルタブ用のプレースホルダーが正しく表示される', () => {
      // チャネルタブ用プレースホルダーをテスト
      render(<TeamsSearchInput {...defaultProps} placeholder="チャネル名で検索" />);

      const input = screen.getByRole('textbox');
      expect(input).toHaveAttribute('placeholder', 'チャネル名で検索');
    });
  });
});
