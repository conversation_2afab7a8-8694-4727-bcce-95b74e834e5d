import * as React from 'react';
import { AddIcon, AcceptIcon } from '@fluentui/react-icons-northstar';
import { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';

// CSS
import './TeamsItemsList.scss';

export interface ITeamsItemsListProps {
  filteredChatItems: IUserChatItem[];
  selectedItems: Set<string>;
  isLoading: boolean;
  error: string | null;
  onItemToggle: (id: string) => void;
  onKeyDown: (event: React.KeyboardEvent, id: string) => void;
  className?: string;
}

/**
 * TeamsItemsList
 * チャット・チャネル一覧表示を担当するコンポーネント
 */
const TeamsItemsList: React.FC<ITeamsItemsListProps> = (props) => {
  const {
    filteredChatItems,
    selectedItems,
    isLoading,
    error,
    onItemToggle,
    onKeyDown,
    className = '',
  } = props;

  // ローディング状態の表示
  if (isLoading) {
    return (
      <div className={`teams-items-list ${className}`}>
        <div className="teams-items-list-loading">
          <p>チャットとチャネルを読み込み中...</p>
        </div>
      </div>
    );
  }

  // エラー状態の表示
  if (error) {
    return (
      <div className={`teams-items-list ${className}`}>
        <div className="teams-items-list-error">
          <p>
            エラーが発生しました:
            {error}
          </p>
        </div>
      </div>
    );
  }

  // アイテムが見つからない場合の表示
  if (filteredChatItems.length === 0) {
    return (
      <div className={`teams-items-list ${className}`}>
        <div className="teams-items-list-no-results">
          <p>該当するチャットまたはチャネルが見つかりませんでした。</p>
        </div>
      </div>
    );
  }

  // アイテム一覧の表示
  return (
    <div className={`teams-items-list ${className}`}>
      <div className="teams-items-list-container">
        {filteredChatItems.map((item) => {
          const isSelected = selectedItems.has(item.id);
          const itemClassName = `teams-items-list-item${isSelected ? ' selected' : ''}`;
          
          return (
            <div
              key={item.id}
              className={itemClassName}
              onClick={() => onItemToggle(item.id)}
              onKeyDown={(event) => onKeyDown(event, item.id)}
              role="button"
              tabIndex={0}
              aria-pressed={isSelected}
              style={{ cursor: 'pointer' }}
            >
              <div className="teams-items-list-item-content">
                <span className="teams-items-list-item-name">{item.name}</span>
              </div>
              <div className="teams-items-list-item-icon">
                {isSelected ? (
                  <AcceptIcon
                    className="teams-items-list-item-icon-selected"
                    style={{
                      color: 'var(--color-guide-brand-icon)',
                      fontSize: '20px',
                      transform: 'scale(1.1)',
                      transition: 'transform 0.2s ease, color 0.2s ease',
                    }}
                  />
                ) : (
                  <AddIcon
                    className="teams-items-list-item-icon-unselected"
                    style={{
                      color: 'var(--color-guide-foreground-2)',
                      fontSize: '20px',
                      transition: 'transform 0.2s ease, color 0.2s ease',
                    }}
                  />
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TeamsItemsList;
