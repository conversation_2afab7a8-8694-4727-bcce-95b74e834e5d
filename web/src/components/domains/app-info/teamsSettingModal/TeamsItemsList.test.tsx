import * as React from 'react';
import '@testing-library/jest-dom';
import { render, fireEvent, screen } from '@testing-library/react';
import TeamsItemsList, { ITeamsItemsListProps } from './TeamsItemsList';
import { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';

// environment をモック
jest.mock('../../../../utilities/environment', () => ({
  __esModule: true,
  default: {
    REACT_APP_API_URL: 'https://example.com/api',
  },
}));

// テスト用のモックデータ
const createMockUserChatItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
  id: 'test-chat-id',
  name: 'テストチャット',
  type: 'チャット',
  chatType: 'oneOnOne',
  ...overrides,
});

const createMockChannelItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
  id: 'test-channel-id',
  name: 'テストチャネル',
  type: 'チャネル',
  chatType: 'channel',
  teamId: 'test-team-id',
  ...overrides,
});

describe('TeamsItemsList', () => {
  // モック関数
  const onItemToggleMock = jest.fn();
  const onKeyDownMock = jest.fn();

  // テスト用データ
  const mockChatItems = [
    createMockUserChatItem({ id: 'chat-1', name: 'チャット1' }),
    createMockUserChatItem({ id: 'chat-2', name: 'チャット2' }),
    createMockChannelItem({ id: 'channel-1', name: 'チャネル1' }),
  ];

  // デフォルトのprops
  const defaultProps: ITeamsItemsListProps = {
    filteredChatItems: mockChatItems,
    selectedItems: new Set(),
    isLoading: false,
    error: null,
    onItemToggle: onItemToggleMock,
    onKeyDown: onKeyDownMock,
  };

  beforeEach(() => {
    // 各テスト前にモックをクリア
    onItemToggleMock.mockClear();
    onKeyDownMock.mockClear();
  });

  describe('基本的なレンダリングのテスト', () => {
    it('アイテム一覧が正常にレンダリングされる', () => {
      // 基本的なレンダリングをテスト
      render(<TeamsItemsList {...defaultProps} />);

      expect(screen.getByText('チャット1')).toBeInTheDocument();
      expect(screen.getByText('チャット2')).toBeInTheDocument();
      expect(screen.getByText('チャネル1')).toBeInTheDocument();
    });

    it('選択されたアイテムが正しく表示される', () => {
      // 選択状態の表示をテスト
      const selectedItems = new Set(['chat-1']);
      render(<TeamsItemsList {...defaultProps} selectedItems={selectedItems} />);

      const chatItem1 = screen.getByText('チャット1').closest('.teams-items-list-item');
      const chatItem2 = screen.getByText('チャット2').closest('.teams-items-list-item');

      expect(chatItem1).toHaveClass('selected');
      expect(chatItem2).not.toHaveClass('selected');
    });

    it('カスタムクラス名が適用される', () => {
      // カスタムクラス名の適用をテスト
      const customClassName = 'custom-items-list';
      const { container } = render(
        <TeamsItemsList {...defaultProps} className={customClassName} />
      );

      const itemsListContainer = container.querySelector('.teams-items-list');
      expect(itemsListContainer).toHaveClass(customClassName);
    });
  });

  describe('ローディング状態のテスト', () => {
    it('ローディング中は適切なメッセージが表示される', () => {
      // ローディング状態をテスト
      render(<TeamsItemsList {...defaultProps} isLoading={true} />);

      expect(screen.getByText('チャットとチャネルを読み込み中...')).toBeInTheDocument();
      expect(screen.queryByText('チャット1')).not.toBeInTheDocument();
    });

    it('ローディング中はアイテム一覧が表示されない', () => {
      // ローディング中のアイテム非表示をテスト
      render(<TeamsItemsList {...defaultProps} isLoading={true} />);

      mockChatItems.forEach(item => {
        expect(screen.queryByText(item.name)).not.toBeInTheDocument();
      });
    });
  });

  describe('エラー状態のテスト', () => {
    it('エラー時は適切なメッセージが表示される', () => {
      // エラー状態をテスト
      const errorMessage = 'テストエラーメッセージ';
      render(<TeamsItemsList {...defaultProps} error={errorMessage} />);

      expect(screen.getByText(/エラーが発生しました:/)).toBeInTheDocument();
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      expect(screen.queryByText('チャット1')).not.toBeInTheDocument();
    });

    it('エラー時はアイテム一覧が表示されない', () => {
      // エラー時のアイテム非表示をテスト
      render(<TeamsItemsList {...defaultProps} error="エラー" />);

      mockChatItems.forEach(item => {
        expect(screen.queryByText(item.name)).not.toBeInTheDocument();
      });
    });
  });

  describe('空の状態のテスト', () => {
    it('アイテムが空の場合は適切なメッセージが表示される', () => {
      // 空の状態をテスト
      render(<TeamsItemsList {...defaultProps} filteredChatItems={[]} />);

      expect(screen.getByText('該当するチャットまたはチャネルが見つかりませんでした。')).toBeInTheDocument();
    });

    it('アイテムが空の場合はアイテム一覧が表示されない', () => {
      // 空の状態でのアイテム非表示をテスト
      render(<TeamsItemsList {...defaultProps} filteredChatItems={[]} />);

      mockChatItems.forEach(item => {
        expect(screen.queryByText(item.name)).not.toBeInTheDocument();
      });
    });
  });

  describe('クリックイベントのテスト', () => {
    it('アイテムクリック時にonItemToggleが呼ばれる', () => {
      // クリックイベントをテスト
      render(<TeamsItemsList {...defaultProps} />);

      const chatItem1 = screen.getByText('チャット1');
      fireEvent.click(chatItem1);

      expect(onItemToggleMock).toHaveBeenCalledTimes(1);
      expect(onItemToggleMock).toHaveBeenCalledWith('chat-1');
    });

    it('複数のアイテムをクリックできる', () => {
      // 複数クリックをテスト
      render(<TeamsItemsList {...defaultProps} />);

      const chatItem1 = screen.getByText('チャット1');
      const chatItem2 = screen.getByText('チャット2');

      fireEvent.click(chatItem1);
      fireEvent.click(chatItem2);

      expect(onItemToggleMock).toHaveBeenCalledTimes(2);
      expect(onItemToggleMock).toHaveBeenNthCalledWith(1, 'chat-1');
      expect(onItemToggleMock).toHaveBeenNthCalledWith(2, 'chat-2');
    });

    it('選択済みアイテムをクリックできる', () => {
      // 選択済みアイテムのクリックをテスト
      const selectedItems = new Set(['chat-1']);
      render(<TeamsItemsList {...defaultProps} selectedItems={selectedItems} />);

      const chatItem1 = screen.getByText('チャット1');
      fireEvent.click(chatItem1);

      expect(onItemToggleMock).toHaveBeenCalledTimes(1);
      expect(onItemToggleMock).toHaveBeenCalledWith('chat-1');
    });
  });

  describe('キーボードイベントのテスト', () => {
    it('キーダウン時にonKeyDownが呼ばれる', () => {
      // キーボードイベントをテスト
      render(<TeamsItemsList {...defaultProps} />);

      const chatItem1 = screen.getByText('チャット1').closest('.teams-items-list-item');
      fireEvent.keyDown(chatItem1!, { key: 'Enter' });

      expect(onKeyDownMock).toHaveBeenCalledTimes(1);
      expect(onKeyDownMock).toHaveBeenCalledWith(expect.any(Object), 'chat-1');
    });

    it('異なるキーでもonKeyDownが呼ばれる', () => {
      // 異なるキーでのイベントをテスト
      render(<TeamsItemsList {...defaultProps} />);

      const chatItem1 = screen.getByText('チャット1').closest('.teams-items-list-item');
      fireEvent.keyDown(chatItem1!, { key: ' ' });

      expect(onKeyDownMock).toHaveBeenCalledTimes(1);
      expect(onKeyDownMock).toHaveBeenCalledWith(expect.any(Object), 'chat-1');
    });
  });

  describe('アクセシビリティのテスト', () => {
    it('適切なroleが設定されている', () => {
      // roleの設定をテスト
      render(<TeamsItemsList {...defaultProps} />);

      const items = screen.getAllByRole('button');
      expect(items).toHaveLength(mockChatItems.length);
    });

    it('aria-pressedが正しく設定されている', () => {
      // aria-pressedの設定をテスト
      const selectedItems = new Set(['chat-1']);
      render(<TeamsItemsList {...defaultProps} selectedItems={selectedItems} />);

      const chatItem1 = screen.getByText('チャット1').closest('.teams-items-list-item');
      const chatItem2 = screen.getByText('チャット2').closest('.teams-items-list-item');

      expect(chatItem1).toHaveAttribute('aria-pressed', 'true');
      expect(chatItem2).toHaveAttribute('aria-pressed', 'false');
    });

    it('tabIndexが設定されている', () => {
      // tabIndexの設定をテスト
      render(<TeamsItemsList {...defaultProps} />);

      const items = screen.getAllByRole('button');
      items.forEach(item => {
        expect(item).toHaveAttribute('tabIndex', '0');
      });
    });

    it('アイテムにフォーカスできる', () => {
      // フォーカスのテスト
      render(<TeamsItemsList {...defaultProps} />);

      const chatItem1 = screen.getByText('チャット1').closest('.teams-items-list-item') as HTMLElement;
      chatItem1.focus();

      expect(chatItem1).toHaveFocus();
    });
  });

  describe('アイコン表示のテスト', () => {
    it('未選択アイテムにはAddIconが表示される', () => {
      // 未選択アイコンをテスト
      render(<TeamsItemsList {...defaultProps} />);

      const addIcons = document.querySelectorAll('.teams-items-list-item-icon-unselected');
      expect(addIcons).toHaveLength(mockChatItems.length);
    });

    it('選択済みアイテムにはAcceptIconが表示される', () => {
      // 選択済みアイコンをテスト
      const selectedItems = new Set(['chat-1', 'channel-1']);
      render(<TeamsItemsList {...defaultProps} selectedItems={selectedItems} />);

      const acceptIcons = document.querySelectorAll('.teams-items-list-item-icon-selected');
      const addIcons = document.querySelectorAll('.teams-items-list-item-icon-unselected');
      
      expect(acceptIcons).toHaveLength(2); // 選択済み
      expect(addIcons).toHaveLength(1); // 未選択
    });
  });
});
