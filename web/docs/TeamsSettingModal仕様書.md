# TeamsSettingModal 仕様書

## 運用保守テスト項目

### 基本機能テスト
- [ ] モーダルを開くとユーザーのTeamsチャット・チャネル一覧が表示される
- [ ] チャット/チャネルタブの切り替えが正常に動作する
- [ ] 検索機能でチャット・チャネル名による絞り込みができる
- [ ] 選択したアイテムが選択済みエリアに表示される

### 選択制限テスト
- [ ] 最大10個までアイテムを選択できる
- [ ] 11個目を選択しようとするとトースターメッセージが表示される
- [ ] 選択済みアイテムの個別削除ができる
- [ ] 選択状態のアイコンが正しく切り替わる（AddIcon ↔ AcceptIcon）

### 保存機能テスト
- [ ] 選択したアイテムがデータベースに正しく保存される
- [ ] 保存済みアイテムがモーダル再表示時に選択状態で表示される
- [ ] 保存処理中にローディング状態が表示される
- [ ] 保存完了後にモーダルが自動で閉じる

### エラーハンドリングテスト
- [ ] Graph API接続エラー時に適切なエラーメッセージが表示される
- [ ] 保存API失敗時にエラーハンドリングされる
- [ ] ネットワークエラー時でもモーダルが開ける

### UI/UXテスト
- [ ] PC・スマートフォンで適切に表示される
- [ ] キーボード操作（Enter、Space）で選択できる
- [ ] アクセシビリティ（ARIA属性）が適切に設定されている
- [ ] ローディング状態が視覚的に分かりやすく表示される

## 概要

TeamsSettingModalは、Microsoft Teamsのチャットとチャネルを検索対象として選択・管理するためのモーダルコンポーネントです。ユーザーが参加しているTeamsのチャットとチャネルを一覧表示し、最大10個まで選択して保存できます。

## 主要機能

### 1. チャット・チャネル一覧表示
- Microsoft Graph APIを使用してユーザーが参加しているチャットとチャネルを取得
- チャットとチャネルをタブで切り替えて表示
- 各タブには該当する件数を表示

### 2. 検索機能
- チャット名またはチャネル名での絞り込み検索
- リアルタイム検索（入力と同時にフィルタリング）
- タブ切り替え時に検索クエリをクリア

### 3. 選択機能
- 最大10個のチャット・チャネルを選択可能
- 選択上限に達した場合はトースターメッセージで警告
- 選択されたアイテムは専用エリアに表示
- 個別の選択解除が可能

### 4. 保存機能
- 選択されたアイテムをデータベースに保存
- 既存の保存済みアイテムとの差分を自動計算
- 削除対象と追加対象を適切に処理
- 保存完了後に自動でモーダルを閉じる

## コンポーネント構成

### メインコンポーネント
- **TeamsSettingModal.tsx**: メインのモーダルコンポーネント

### サブコンポーネント
- **TeamsSettingTabs.tsx**: チャット/チャネル切り替えタブ
- **SelectedItemsList.tsx**: 選択されたアイテム一覧表示

### 使用するHooks
- **useUserChatsAndChannelsAccessor**: Microsoft Graph APIからチャット・チャネル取得
- **useTeamsChatsApiAccessor**: 保存・削除・取得のAPI操作
- **useComponentInitUtility**: コンポーネント初期化とトークン管理
- **useMessageToasterBehavior**: トースターメッセージ表示

## データ型定義

### IUserChatItem
```typescript
interface IUserChatItem {
  id: string;                    // チャットIDまたはチャネルID
  name: string;                  // 表示名
  type: 'チャット' | 'チャネル';    // アイテムタイプ
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'TeamsChannel';
  teamId?: string;               // チャネルの場合のチームID
}
```

### ITeamsChatsRequest
```typescript
interface ITeamsChatsRequest {
  countId: number;               // 選択順序（1-10）
  chatType: 'oneOnOne' | 'group' | 'meeting' | 'TeamsChannel';
  chatId?: string;               // チャット用ID
  teamId?: string;               // チャネル用チームID
  channelId?: string;            // チャネル用ID
}
```

## 状態管理

### 主要な状態
- `searchQuery`: 検索クエリ文字列
- `allChatItems`: 全チャット・チャネルデータ
- `filteredChatItems`: フィルタリング後のデータ
- `selectedItems`: 選択されたアイテムのIDセット
- `savedItems`: 保存済みアイテムのIDセット
- `activeTab`: アクティブなタブ（チャット/チャネル）
- `isSaving`: 保存処理中フラグ
- `isLoadingSavedItems`: 保存済みアイテム読み込み中フラグ
- `isSaveCompleted`: 保存完了フラグ

### ローディング状態
- `isLoadingData`: データ取得中の統合ローディング状態
- Microsoft Graph APIとデータベースからの並行データ取得

## API連携

### Microsoft Graph API
- `/me/chats`: ユーザーのチャット一覧取得
- `/me/joinedTeams`: 参加チーム一覧取得
- `/teams/{teamId}/channels`: チーム内チャネル一覧取得

### 内部API
- `POST /api/users/{userId}/teams-chats`: チャット・チャネル設定保存
- `GET /api/users/{userId}/teams-chats`: 保存済み設定取得
- `DELETE /api/users/{userId}/teams-chats/{chatId}`: 設定削除

## UI/UX仕様

### レスポンシブ対応
- PC用とスマートフォン用の閉じるボタンを個別配置
- フルードレイアウトで画面サイズに対応

### アクセシビリティ
- キーボード操作対応（Enter、Spaceキー）
- ARIA属性による適切なロール設定
- スクリーンリーダー対応

### 視覚的フィードバック
- 選択状態のアイコン変化（AddIcon ↔ AcceptIcon）
- ローディング状態の表示
- エラー状態の表示
- 保存完了時の視覚的フィードバック

## エラーハンドリング

### 主要なエラーケース
1. Microsoft Graph APIアクセスエラー
2. 保存済みアイテム取得エラー
3. 保存・削除API呼び出しエラー
4. トークンプロバイダー未定義エラー

### エラー表示
- エラーメッセージをモーダル内に表示
- エラーが発生してもモーダルは開けるように設計
- 上位コンポーネントへのエラー再スロー

## 制限事項

### 選択制限
- 最大10個のアイテムまで選択可能
- 上限到達時はトースターメッセージで通知

### データ制限
- Microsoft Graph APIの制限に依存
- チャット履歴の開始日時でソート

## 保存処理の詳細

### 保存フロー
1. 削除対象アイテムの特定（保存済みだが選択されていないアイテム）
2. 削除処理の実行（並行処理）
3. 選択アイテムのUpsert処理（直列処理で順序保持）
4. 保存完了状態の表示
5. 0.8秒後にモーダル自動クローズ

### データ変換
- フロントエンドのIUserChatItemからバックエンドのITeamsChatsRequestへ変換
- countIdは選択順序に基づいて1から順番に割り当て
- チャットとチャネルで異なるフィールド設定

## 今後の拡張可能性

### 機能拡張
- 選択上限数の動的変更
- ソート機能の追加
- お気に入り機能
- 最近使用したアイテムの優先表示

### パフォーマンス改善
- 仮想スクロールの導入
- データキャッシュ機能
- 増分データ取得
