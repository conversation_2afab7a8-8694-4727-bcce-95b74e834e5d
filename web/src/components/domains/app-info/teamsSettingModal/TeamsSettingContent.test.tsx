// import * as React from 'react';
// import '@testing-library/jest-dom';
// import { render, screen } from '@testing-library/react';
// import TeamsSettingContent, { ITeamsSettingContentProps } from './TeamsSettingContent';
// import { IUserChatItem } from '../../../../hooks/accessors/useUserChatsAndChannelsAccessor';
// import { TeamsSettingTabType } from './TeamsSettingTabs';

// // environment をモック
// jest.mock('../../../../utilities/environment', () => ({
//   __esModule: true,
//   default: {
//     REACT_APP_API_URL: 'https://example.com/api',
//   },
// }));

// // 子コンポーネントをモック
// jest.mock('./TeamsSettingTabs', () => {
//   return function MockTeamsSettingTabs(props: any) {
//     return (
//       <div data-testid="teams-setting-tabs">
//         <span>Active Tab: {props.activeTab}</span>
//         <span>Chat Count: {props.chatCount}</span>
//         <span>Channel Count: {props.channelCount}</span>
//         <span>Disabled: {props.disabled.toString()}</span>
//       </div>
//     );
//   };
// });

// jest.mock('./SelectedItemsList', () => {
//   return function MockSelectedItemsList(props: any) {
//     return (
//       <div data-testid="selected-items-list">
//         <span>Selected Items: {props.selectedItems.size}</span>
//         <span>All Items: {props.allChatItems.length}</span>
//       </div>
//     );
//   };
// });

// jest.mock('./TeamsSearchInput', () => {
//   return function MockTeamsSearchInput(props: any) {
//     return (
//       <div data-testid="teams-search-input">
//         <span>Placeholder: {props.placeholder}</span>
//         <span>Value: {props.value}</span>
//         <span>Disabled: {props.disabled.toString()}</span>
//       </div>
//     );
//   };
// });

// jest.mock('./TeamsItemsList', () => {
//   return function MockTeamsItemsList(props: any) {
//     return (
//       <div data-testid="teams-items-list">
//         <span>Filtered Items: {props.filteredChatItems.length}</span>
//         <span>Selected Items: {props.selectedItems.size}</span>
//         <span>Loading: {props.isLoading.toString()}</span>
//         <span>Error: {props.error || 'null'}</span>
//       </div>
//     );
//   };
// });

// jest.mock('./TeamsSaveButton', () => {
//   return function MockTeamsSaveButton(props: any) {
//     return (
//       <div data-testid="teams-save-button">
//         <span>Saving: {props.isSaving.toString()}</span>
//         <span>Completed: {props.isSaveCompleted.toString()}</span>
//         <span>Disabled: {props.isDisabled.toString()}</span>
//       </div>
//     );
//   };
// });

// // テスト用のモックデータ
// const createMockUserChatItem = (overrides: Partial<IUserChatItem> = {}): IUserChatItem => ({
//   id: 'test-chat-id',
//   name: 'テストチャット',
//   type: 'チャット',
//   chatType: 'oneOnOne',
//   ...overrides,
// });

// describe('TeamsSettingContent', () => {
//   // モック関数
//   const onTabChangeMock = jest.fn();
//   const onSearchQueryChangeMock = jest.fn();
//   const onItemToggleMock = jest.fn();
//   const onRemoveSelectedItemMock = jest.fn();
//   const onKeyDownMock = jest.fn();
//   const onSaveMock = jest.fn();

//   // テスト用データ
//   const mockChatItems = [
//     createMockUserChatItem({ id: 'chat-1', name: 'チャット1' }),
//     createMockUserChatItem({ id: 'chat-2', name: 'チャット2' }),
//   ];

//   // デフォルトのprops
//   const defaultProps: ITeamsSettingContentProps = {
//     // データ関連
//     allChatItems: mockChatItems,
//     filteredChatItems: mockChatItems,
//     selectedItems: new Set(['chat-1']),
//     isLoadingData: false,
//     error: null,

//     // タブ関連
//     activeTab: TeamsSettingTabType.CHAT,
//     chatCount: 2,
//     channelCount: 0,

//     // 検索関連
//     searchQuery: '',
//     searchPlaceholder: 'チャット名で検索',

//     // 保存関連
//     isSaving: false,
//     isSaveCompleted: false,
//     isSaveDisabled: false,
//     saveButtonContent: '保存',

//     // イベントハンドラー
//     onTabChange: onTabChangeMock,
//     onSearchQueryChange: onSearchQueryChangeMock,
//     onItemToggle: onItemToggleMock,
//     onRemoveSelectedItem: onRemoveSelectedItemMock,
//     onKeyDown: onKeyDownMock,
//     onSave: onSaveMock,
//   };

//   beforeEach(() => {
//     // 各テスト前にモックをクリア
//     onTabChangeMock.mockClear();
//     onSearchQueryChangeMock.mockClear();
//     onItemToggleMock.mockClear();
//     onRemoveSelectedItemMock.mockClear();
//     onKeyDownMock.mockClear();
//     onSaveMock.mockClear();
//   });

//   describe('基本的なレンダリングのテスト', () => {
//     it('コンポーネントが正常にレンダリングされる', () => {
//       // 基本的なレンダリングをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       expect(screen.getByText('Teams設定')).toBeInTheDocument();
//       expect(screen.getByText('検索対象を選択できます。')).toBeInTheDocument();
//     });

//     it('すべての子コンポーネントがレンダリングされる', () => {
//       // 子コンポーネントのレンダリングをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       expect(screen.getByTestId('teams-setting-tabs')).toBeInTheDocument();
//       expect(screen.getByTestId('selected-items-list')).toBeInTheDocument();
//       expect(screen.getByTestId('teams-search-input')).toBeInTheDocument();
//       expect(screen.getByTestId('teams-items-list')).toBeInTheDocument();
//       expect(screen.getByTestId('teams-save-button')).toBeInTheDocument();
//     });

//     it('カスタムクラス名が適用される', () => {
//       // カスタムクラス名の適用をテスト
//       const customClassName = 'custom-content';
//       const { container } = render(
//         <TeamsSettingContent {...defaultProps} className={customClassName} />
//       );

//       const contentContainer = container.querySelector('.teams-setting-content');
//       expect(contentContainer).toHaveClass(customClassName);
//     });
//   });

//   describe('子コンポーネントへのprops渡しのテスト', () => {
//     it('TeamsSettingTabsに正しいpropsが渡される', () => {
//       // TeamsSettingTabsへのprops渡しをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       const tabsComponent = screen.getByTestId('teams-setting-tabs');
//       expect(tabsComponent).toHaveTextContent('Active Tab: チャット');
//       expect(tabsComponent).toHaveTextContent('Chat Count: 2');
//       expect(tabsComponent).toHaveTextContent('Channel Count: 0');
//       expect(tabsComponent).toHaveTextContent('Disabled: false');
//     });

//     it('SelectedItemsListに正しいpropsが渡される', () => {
//       // SelectedItemsListへのprops渡しをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       const selectedItemsComponent = screen.getByTestId('selected-items-list');
//       expect(selectedItemsComponent).toHaveTextContent('Selected Items: 1');
//       expect(selectedItemsComponent).toHaveTextContent('All Items: 2');
//     });

//     it('TeamsSearchInputに正しいpropsが渡される', () => {
//       // TeamsSearchInputへのprops渡しをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       const searchInputComponent = screen.getByTestId('teams-search-input');
//       expect(searchInputComponent).toHaveTextContent('Placeholder: チャット名で検索');
//       expect(searchInputComponent).toHaveTextContent('Value: ');
//       expect(searchInputComponent).toHaveTextContent('Disabled: false');
//     });

//     it('TeamsItemsListに正しいpropsが渡される', () => {
//       // TeamsItemsListへのprops渡しをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       const itemsListComponent = screen.getByTestId('teams-items-list');
//       expect(itemsListComponent).toHaveTextContent('Filtered Items: 2');
//       expect(itemsListComponent).toHaveTextContent('Selected Items: 1');
//       expect(itemsListComponent).toHaveTextContent('Loading: false');
//       expect(itemsListComponent).toHaveTextContent('Error: null');
//     });

//     it('TeamsSaveButtonに正しいpropsが渡される', () => {
//       // TeamsSaveButtonへのprops渡しをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       const saveButtonComponent = screen.getByTestId('teams-save-button');
//       expect(saveButtonComponent).toHaveTextContent('Saving: false');
//       expect(saveButtonComponent).toHaveTextContent('Completed: false');
//       expect(saveButtonComponent).toHaveTextContent('Disabled: false');
//     });
//   });

//   describe('ローディング状態のテスト', () => {
//     it('ローディング中は子コンポーネントが適切に無効化される', () => {
//       // ローディング状態をテスト
//       render(<TeamsSettingContent {...defaultProps} isLoadingData={true} />);

//       const tabsComponent = screen.getByTestId('teams-setting-tabs');
//       const searchInputComponent = screen.getByTestId('teams-search-input');
//       const itemsListComponent = screen.getByTestId('teams-items-list');

//       expect(tabsComponent).toHaveTextContent('Disabled: true');
//       expect(searchInputComponent).toHaveTextContent('Disabled: true');
//       expect(itemsListComponent).toHaveTextContent('Loading: true');
//     });
//   });

//   describe('エラー状態のテスト', () => {
//     it('エラーがある場合、TeamsItemsListにエラーが渡される', () => {
//       // エラー状態をテスト
//       const errorMessage = 'テストエラーメッセージ';
//       render(<TeamsSettingContent {...defaultProps} error={errorMessage} />);

//       const itemsListComponent = screen.getByTestId('teams-items-list');
//       expect(itemsListComponent).toHaveTextContent(`Error: ${errorMessage}`);
//     });
//   });

//   describe('保存状態のテスト', () => {
//     it('保存中状態がTeamsSaveButtonに正しく渡される', () => {
//       // 保存中状態をテスト
//       render(<TeamsSettingContent {...defaultProps} isSaving={true} />);

//       const saveButtonComponent = screen.getByTestId('teams-save-button');
//       expect(saveButtonComponent).toHaveTextContent('Saving: true');
//     });

//     it('保存完了状態がTeamsSaveButtonに正しく渡される', () => {
//       // 保存完了状態をテスト
//       render(<TeamsSettingContent {...defaultProps} isSaveCompleted={true} />);

//       const saveButtonComponent = screen.getByTestId('teams-save-button');
//       expect(saveButtonComponent).toHaveTextContent('Completed: true');
//     });

//     it('保存無効化状態がTeamsSaveButtonに正しく渡される', () => {
//       // 保存無効化状態をテスト
//       render(<TeamsSettingContent {...defaultProps} isSaveDisabled={true} />);

//       const saveButtonComponent = screen.getByTestId('teams-save-button');
//       expect(saveButtonComponent).toHaveTextContent('Disabled: true');
//     });
//   });

//   describe('検索状態のテスト', () => {
//     it('検索クエリがTeamsSearchInputに正しく渡される', () => {
//       // 検索クエリをテスト
//       const searchQuery = 'テスト検索';
//       render(<TeamsSettingContent {...defaultProps} searchQuery={searchQuery} />);

//       const searchInputComponent = screen.getByTestId('teams-search-input');
//       expect(searchInputComponent).toHaveTextContent(`Value: ${searchQuery}`);
//     });

//     it('検索プレースホルダーがTeamsSearchInputに正しく渡される', () => {
//       // 検索プレースホルダーをテスト
//       const placeholder = 'カスタムプレースホルダー';
//       render(<TeamsSettingContent {...defaultProps} searchPlaceholder={placeholder} />);

//       const searchInputComponent = screen.getByTestId('teams-search-input');
//       expect(searchInputComponent).toHaveTextContent(`Placeholder: ${placeholder}`);
//     });
//   });

//   describe('レイアウト構造のテスト', () => {
//     it('適切なCSSクラスが設定されている', () => {
//       // CSSクラスの設定をテスト
//       const { container } = render(<TeamsSettingContent {...defaultProps} />);

//       expect(container.querySelector('.teams-setting-content')).toBeInTheDocument();
//       expect(container.querySelector('.teams-setting-content-header')).toBeInTheDocument();
//       expect(container.querySelector('.teams-setting-content-main')).toBeInTheDocument();
//       expect(container.querySelector('.teams-setting-content-tabs')).toBeInTheDocument();
//       expect(container.querySelector('.teams-setting-content-search')).toBeInTheDocument();
//       expect(container.querySelector('.teams-setting-content-selected')).toBeInTheDocument();
//       expect(container.querySelector('.teams-setting-content-items')).toBeInTheDocument();
//       expect(container.querySelector('.teams-setting-content-save')).toBeInTheDocument();
//     });

//     it('ヘッダーに適切なタイトルが表示される', () => {
//       // ヘッダータイトルをテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       const title = screen.getByRole('heading', { level: 3 });
//       expect(title).toHaveTextContent('Teams設定');
//       expect(title).toHaveClass('teams-setting-content-title');
//     });

//     it('説明文が正しく表示される', () => {
//       // 説明文をテスト
//       render(<TeamsSettingContent {...defaultProps} />);

//       const description = screen.getByText('検索対象を選択できます。');
//       expect(description).toHaveClass('teams-setting-content-description');
//     });
//   });
// });
