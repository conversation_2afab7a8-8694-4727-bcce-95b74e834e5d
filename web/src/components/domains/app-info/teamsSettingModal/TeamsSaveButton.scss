@import '../../../../styles/variables';

.teams-save-button {
  margin-top: 20px;
  padding: 0 20px;
}

.teams-save-button-element {
  width: 100%;
  min-height: 44px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 4px;
  transition: all 0.3s ease;

  // 通常状態
  &:not(.completed):not(.saving) {
    background-color: var(--color-guide-brand-background);
    border-color: var(--color-guide-brand-border);
    color: var(--color-guide-brand-foreground);

    &:hover:not(:disabled) {
      background-color: var(--color-guide-brand-background-hover);
      border-color: var(--color-guide-brand-border-hover);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }
  }

  // 保存中状態
  &.saving {
    background-color: var(--color-guide-foreground-5);
    border-color: var(--color-guide-foreground-4);
    color: var(--color-guide-foreground-2);
    cursor: not-allowed;

    .ui-loader {
      margin-right: 8px;
    }
  }

  // 保存完了状態
  &.completed {
    background-color: #107c10 !important;
    border-color: #107c10 !important;
    color: white !important;
    animation: completedPulse 0.6s ease-in-out;

    &:hover {
      background-color: #0e6e0e !important;
      border-color: #0e6e0e !important;
    }
  }

  // 無効化状態
  &:disabled {
    background-color: var(--color-guide-foreground-6);
    border-color: var(--color-guide-foreground-5);
    color: var(--color-guide-foreground-3);
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}

// 保存完了時のアニメーション
@keyframes completedPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 124, 16, 0.7);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 8px rgba(16, 124, 16, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 124, 16, 0);
  }
}

/* ダークテーマ対応 */
@media (prefers-color-scheme: dark) {
  .teams-save-button-element {
    // 通常状態（ダークテーマ）
    &:not(.completed):not(.saving) {
      background-color: var(--color-guide-brand-background-dark);
      border-color: var(--color-guide-brand-border-dark);
      color: var(--color-guide-brand-foreground-light);

      &:hover:not(:disabled) {
        background-color: var(--color-guide-brand-background-hover-dark);
        border-color: var(--color-guide-brand-border-hover-dark);
      }
    }

    // 保存中状態（ダークテーマ）
    &.saving {
      background-color: var(--color-guide-foreground-4);
      border-color: var(--color-guide-foreground-3);
      color: var(--color-guide-foreground-2);
    }

    // 無効化状態（ダークテーマ）
    &:disabled {
      background-color: var(--color-guide-foreground-5);
      border-color: var(--color-guide-foreground-4);
      color: var(--color-guide-foreground-3);
    }
  }
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
  .teams-save-button {
    margin-top: 16px;
    padding: 0 16px;
  }

  .teams-save-button-element {
    min-height: 48px;
    font-size: 18px;
  }
}

/* フォーカス状態 */
.teams-save-button-element:focus {
  outline: 2px solid var(--color-guide-brand-border);
  outline-offset: 2px;
}

/* ハイコントラストモード対応 */
@media (prefers-contrast: high) {
  .teams-save-button-element {
    border-width: 2px;

    &:not(.completed):not(.saving) {
      border-color: var(--color-guide-foreground-1);
    }

    &.completed {
      border-color: #0e6e0e !important;
    }

    &:disabled {
      border-color: var(--color-guide-foreground-3);
    }
  }
}

/* 動きを減らす設定への対応 */
@media (prefers-reduced-motion: reduce) {
  .teams-save-button-element {
    transition: none;

    &:hover:not(:disabled) {
      transform: none;
    }

    &:active:not(:disabled) {
      transform: none;
    }
  }

  @keyframes completedPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: none;
    }
  }
}
